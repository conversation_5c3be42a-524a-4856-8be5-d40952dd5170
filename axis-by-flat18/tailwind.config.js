/* eslint-disable @typescript-eslint/no-require-imports */
/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ["class", "[data-theme='dark']"],
  safelist: [
    // Ensure dynamic brand token classes aren’t purged
    { pattern: /^(text|bg|border)-axis-(accent-1|accent-2|accent-3)$/ },
    { pattern: /^(bg|border)-axis-(accent-1|accent-2|accent-3)\/10$/ },
    { pattern: /^(bg|border)-axis-(accent-1|accent-2|accent-3)\/20$/ },
    { pattern: /^border-axis-(accent-1|accent-2|accent-3)\/30$/ },
    { pattern: /^border-axis-(accent-1|accent-2|accent-3)\/50$/ },
  ],
  content: [
    "./src/app/**/*.{ts,tsx,mdx}",
    "./src/components/**/*.{ts,tsx,mdx}",
    "./src/pages/**/*.{ts,tsx,mdx}",
    "./src/**/*.{ts,tsx,mdx}"
  ],
  theme: {
    container: {
      center: true,
      padding: "1.5rem",
      screens: { "2xl": "1200px" },
    },
    extend: {
      colors: {
        border: "var(--border)",
        input: "var(--border)",
        ring: "hsl(var(--ring-hsl))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary-hsl))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary-hsl))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive-hsl))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted-hsl))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent-hsl))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },

        // Axis brand tokens (for utilities like border-axis-base if needed)
        "axis-base": "var(--axis-base)",
        "axis-text": "var(--axis-text)",
        "axis-accent-1": "var(--axis-accent-1)",
        "axis-accent-2": "var(--axis-accent-2)",
        "axis-accent-3": "var(--axis-accent-3)",
        "axis-support": "var(--danger)",
      },
      borderRadius: {
        xl: "var(--radius-lg)",
        lg: "var(--radius-md)",
        md: "calc(var(--radius-md) - 0.25rem)",
        sm: "calc(var(--radius-sm) - 0.25rem)",
      },
      boxShadow: {
        sm: "var(--shadow-sm)",
        md: "var(--shadow-md)",
        lg: "var(--shadow-lg)",
      },
      fontFamily: {
        display: ["var(--font-inter)", "Inter", "ui-sans-serif", "system-ui"],
        body: ["var(--font-inter)", "Inter", "ui-sans-serif", "system-ui"],
      },
      keyframes: {
        "accordion-down": { from: { height: 0 }, to: { height: "var(--radix-accordion-content-height)" } },
        "accordion-up":   { from: { height: "var(--radix-accordion-content-height)" }, to: { height: 0 } },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
};
