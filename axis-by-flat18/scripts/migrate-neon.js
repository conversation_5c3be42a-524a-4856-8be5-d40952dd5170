#!/usr/bin/env node
/* eslint-disable @typescript-eslint/no-require-imports */
/*
  Simple Neon migration/setup script.
  Requires DATABASE_URL to be set in the environment.
*/
const { neon } = require('@neondatabase/serverless');

async function main() {
  const url = process.env.DATABASE_URL;
  if (!url) {
    console.error('DATABASE_URL is not set');
    process.exit(1);
  }
  const sql = neon(url);

  // Create extension for gen_random_uuid()
  await sql`create extension if not exists pgcrypto`;

  // Leads table
  await sql`
    create table if not exists leads (
      id serial primary key,
      name text not null,
      email text not null,
      company text,
      idea text not null,
      tier text not null,
      timeline text not null,
      budget text not null,
      addon text,
      source text default 'website',
      created_at timestamptz default now()
    )
  `;

  // Invoiceninja events table
  await sql`
    create table if not exists in_events (
      id uuid primary key default gen_random_uuid(),
      event_type text,
      data jsonb,
      created_at timestamptz default now()
    )
  `;

  // Clients table
  await sql`
    create table if not exists clients (
      id uuid primary key default gen_random_uuid(),
      external_id text,
      email text unique,
      name text,
      source text,
      created_at timestamptz default now()
    )
  `;

  // Payments table (includes email + lead_id for linking)
  await sql`
    create table if not exists payments (
      id uuid primary key default gen_random_uuid(),
      external_id text unique,
      reference text,
      amount numeric,
      status text,
      provider text,
      raw jsonb,
      email text,
      lead_id integer references leads(id) on delete set null,
      created_at timestamptz default now()
    )
  `;

  // Helpful indexes
  await sql`create index if not exists idx_leads_email_created_at on leads (email, created_at desc)`;
  await sql`create index if not exists idx_payments_created_at on payments (created_at desc)`;

  console.log('Neon migration completed');
}

main().catch((e) => {
  console.error('Migration failed', e);
  process.exit(1);
});
