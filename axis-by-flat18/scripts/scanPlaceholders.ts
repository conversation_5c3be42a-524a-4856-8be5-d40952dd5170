import fs from "fs";
import path from "path";

type Ref = {
  file: string;
  line: number;
  snippet: string;
  component?: string;
  propAlt?: string;
  width?: string;
  height?: string;
};

type Asset = {
  id: string;
  path: string;
  type: "image"|"video"|"icon"|"other";
  exists: boolean;
  referencedIn: Ref[];
  suggestedPrompt?: string;
};

const ROOT = process.cwd();
const PUBLIC_DIR = path.join(ROOT, "public");
const PLACEHOLDER_DIR = path.join(PUBLIC_DIR, "placeholders");
const SRC_DIR = path.join(ROOT, "src");

const extsToType: Record<string, Asset["type"]> = {
  ".png":"image",".jpg":"image",".jpeg":"image",".webp":"image",".svg":"icon",
  ".mp4":"video",".webm":"video"
};

function walk(dir: string): string[] {
  const out: string[] = [];
  for (const entry of fs.readdirSync(dir, { withFileTypes: true })) {
    const p = path.join(dir, entry.name);
    if (entry.isDirectory()) out.push(...walk(p));
    else out.push(p);
  }
  return out;
}

function listPlaceholderFiles(): Asset[] {
  if (!fs.existsSync(PLACEHOLDER_DIR)) return [];
  const files = walk(PLACEHOLDER_DIR);
  return files.map(f => {
    const ext = path.extname(f).toLowerCase();
    const type = extsToType[ext] ?? "other";
    return {
      id: path.relative(ROOT, f),
      path: path.relative(ROOT, f),
      type,
      exists: fs.existsSync(f),
      referencedIn: [],
    };
  });
}

function grepSourceForPlaceholders(): {file:string, lines:string[]}[] {
  const results: {file:string, lines:string[]}[] = [];
  function scanFile(file:string){
    const text = fs.readFileSync(file,"utf8");
    const lines = text.split(/\r?\n/);
    const hits:string[] = [];
    lines.forEach((ln,i)=>{
      if (ln.includes("/public/placeholders/") || ln.includes("placeholders/") || ln.match(/\/placeholders\/\w+/)) {
        hits.push(`${i+1}:::${ln}`);
      }
      if (ln.match(/src=.*placeholders\//)) {
        hits.push(`${i+1}:::${ln}`);
      }
    });
    if (hits.length) results.push({file, lines:hits});
  }
  function scanDir(dir:string){
    for(const entry of fs.readdirSync(dir,{withFileTypes:true})){
      const p = path.join(dir, entry.name);
      if (entry.isDirectory()) scanDir(p);
      else if (/\.(tsx|ts|jsx|js|mdx|css)$/i.test(p)) scanFile(p);
    }
  }
  if (fs.existsSync(SRC_DIR)) scanDir(SRC_DIR);
  return results;
}

function tryExtract(refLine:string){
  const propAlt = (refLine.match(/alt\s*=\s*["'`](.*?)["'`]/)?.[1]) ?? undefined;
  const width  = (refLine.match(/width\s*=\s*["'`](\d+)[^"'`]*/)?.[1]) ?? undefined;
  const height = (refLine.match(/height\s*=\s*["'`](\d+)[^"'`]*/)?.[1]) ?? undefined;
  const component = (refLine.match(/<([A-Z][A-Za-z0-9]+)/)?.[1]) ?? undefined;
  return {propAlt,width,height,component};
}

function inferPrompt(a:Asset): string {
  if (a.type === "video" && a.path.includes("hero")) {
    return "SORA: 10s seamless loop. Dark geometric grid rotating around a central axis. Electric blue/purple sweeps. Minimal, premium, no text.";
  }
  if (a.type === "icon") {
    if (a.path.includes("tier-start")) return "GPT-5 IMAGE: Minimal line icon: lightning on an axis. Monoline, geometric, export as clean SVG.";
    if (a.path.includes("tier-build")) return "GPT-5 IMAGE: Minimal line icon: expanding radial lines from centre. Monoline, geometric, export as clean SVG.";
    if (a.path.includes("tier-pro"))   return "GPT-5 IMAGE: Minimal line icon: stacked cubes around centre point. Monoline, geometric, export as clean SVG.";
    return "GPT-5 IMAGE: Minimal geometric line icon matching Axis style. Export SVG.";
  }
  if (a.type === "image" && a.path.includes("mockup-")) {
    return "GPT-5 IMAGE: Angled browser mockup showing a clean UI (crypto dashboard / MVP landing / token table). Dark theme, premium, Axis palette (#0D1117,#E6EDF3,#2F81F7,#9D4BFF,#00E0B8). No text.";
  }
  return "GPT-5 IMAGE: Minimalist, dark, premium visual matching Axis palette. No text.";
}

function main(){
  fs.mkdirSync(path.join(ROOT,"reports"), {recursive:true});
  const assets = listPlaceholderFiles();
  const refs = grepSourceForPlaceholders();

  for (const r of refs){
    for (const line of r.lines){
      const [ln, snippet] = line.split(":::");
      for (const a of assets){
        if (snippet.includes(a.path.replace(/^public\//,"/")) || snippet.includes(a.path.replace(/^public\//,"")) || snippet.includes(path.basename(a.path))) {
          const x = tryExtract(snippet);
          a.referencedIn.push({
            file: path.relative(ROOT, r.file),
            line: Number(ln),
            snippet: snippet.trim(),
            component: x.component,
            propAlt: x.propAlt,
            width: x.width,
            height: x.height,
          });
        }
      }
    }
  }

  for (const a of assets){
    a.suggestedPrompt = inferPrompt(a);
  }

  const manifestPath = path.join(ROOT, "reports", "placeholders-manifest.json");
  fs.writeFileSync(manifestPath, JSON.stringify(assets, null, 2), "utf8");

  const lines:string[] = [];
  lines.push("# Axis — Placeholder Assets Report");
  lines.push("");
  lines.push("| Asset | Type | Exists | References | Suggested Prompt |");
  lines.push("|---|---|---|---|---|");
  for (const a of assets){
    const refsCount = a.referencedIn.length;
    lines.push(`| \`${a.path}\` | ${a.type} | ${a.exists ? "✅" : "❌"} | ${refsCount} | ${a.suggestedPrompt?.replace(/\|/g,"/")} |`);
  }
  lines.push("");
  for (const a of assets){
    lines.push(`## ${a.path}`);
    lines.push("");
    lines.push(`**Type:** ${a.type} • **Exists:** ${a.exists ? "Yes" : "No"}`);
    if (a.suggestedPrompt) { lines.push(`**Suggested Prompt:** ${a.suggestedPrompt}`); }
    if (a.referencedIn.length){
      lines.push("");
      lines.push("| File | Line | Component | Alt | Snippet |");
      lines.push("|---|---:|---|---|---|");
      for (const r of a.referencedIn){
        const snip = r.snippet.replace(/\|/g,"/").slice(0,160);
        lines.push(`| \`${r.file}\` | ${r.line} | ${r.component ?? ""} | ${(r.propAlt ?? "").replace(/\|/g,"/")} | \`${snip}\` |`);
      }
    }
    lines.push("");
  }
  const reportPath = path.join(ROOT, "reports", "placeholders-report.md");
  fs.writeFileSync(reportPath, lines.join("\n"), "utf8");

  const dirLines:string[] = [];
  dirLines.push("# Axis — Sora & GPT-5 Asset Generation Directives");
  dirLines.push("");
  dirLines.push("> Generate assets to replace all placeholders. Keep British English in any alt text.");
  dirLines.push("");
  for (const a of assets){
    dirLines.push("----");
    dirLines.push(`## ${a.path}`);
    dirLines.push(`**Type:** ${a.type}`);
    dirLines.push("**Brand palette:** #0D1117 (base), #E6EDF3 (text), #2F81F7, #9D4BFF, #00E0B8.");
    if (a.referencedIn.length){
      const spots = a.referencedIn.map(r=>`\`${r.file}:${r.line}\``).join(", ");
      dirLines.push(`**Used in:** ${spots}`);
    }
    if (a.type === "video") {
      dirLines.push("**SORA PROMPT:**");
      dirLines.push(a.suggestedPrompt || "SORA: Minimal 10s loop matching Axis style. No text.");
      dirLines.push("**Format:** MP4, seamless loop, 1920x1080 preferred, <=8MB if possible.");
    } else {
      dirLines.push("**GPT-5 IMAGE PROMPT:**");
      dirLines.push(a.suggestedPrompt || "GPT-5 IMAGE: Minimal, dark, premium visual. No text.");
      const ext = path.extname(a.path).toLowerCase();
      if (ext === ".svg") {
        dirLines.push("**Format:** Clean SVG, monoline strokes, 24x24 or 48x48 artboard.");
      } else {
        dirLines.push("**Format:** PNG or WEBP, 2x DPR, ~1600px width for hero/mockups; keep file sizes lean.");
      }
      dirLines.push("**Alt text suggestion:** Keep concise, e.g., “Axis hero animation loop” / “Axis tier icon — Start”.");
    }
    dirLines.push("");
  }
  const directivesPath = path.join(ROOT, "reports", "sora_gpt5_directives.md");
  fs.writeFileSync(directivesPath, dirLines.join("\n"), "utf8");

  console.log("Wrote:", manifestPath);
  console.log("Wrote:", reportPath);
  console.log("Wrote:", directivesPath);
}

main();
