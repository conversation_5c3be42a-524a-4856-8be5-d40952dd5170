-- Invoiceninja webhook → Supabase sync tables
-- Create extension if using pgcrypto for gen_random_uuid
-- create extension if not exists pgcrypto;

create table if not exists in_events (
  id uuid primary key default gen_random_uuid(),
  event_type text,
  data jsonb,
  created_at timestamptz default now()
);

create table if not exists clients (
  id uuid primary key default gen_random_uuid(),
  external_id text,
  email text unique,
  name text,
  source text,
  created_at timestamptz default now()
);

create table if not exists payments (
  id uuid primary key default gen_random_uuid(),
  external_id text unique,
  reference text,
  amount numeric,
  status text,
  provider text,
  raw jsonb,
  created_at timestamptz default now()
);

