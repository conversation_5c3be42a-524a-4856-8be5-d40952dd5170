{"name": "axis-by-flat18", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint", "migrate:neon": "node ./scripts/migrate-neon.js"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@neondatabase/serverless": "^0.10.4", "@next/font": "^14.2.15", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "@vercel/speed-insights": "^1.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.12", "lottie-react": "^2.4.1", "lucide-react": "^0.542.0", "next": "15.5.2", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^4.1.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "20.11.30", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "10.4.20", "eslint": "^9", "eslint-config-next": "15.5.2", "postcss": "8.4.47", "tailwindcss": "3.4.13", "ts-node": "10.9.2", "typescript": "^5"}}