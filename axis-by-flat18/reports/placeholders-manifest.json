[{"id": "public/placeholders/README.md", "path": "public/placeholders/README.md", "type": "other", "exists": true, "referencedIn": [], "suggestedPrompt": "GPT-5 IMAGE: Minimalist, dark, premium visual matching Axis palette. No text."}, {"id": "public/placeholders/img/tier-build.svg", "path": "public/placeholders/img/tier-build.svg", "type": "icon", "exists": true, "referencedIn": [{"file": "src/components/pricing/pricing-cards.tsx", "line": 32, "snippet": "icon: \"/placeholders/img/tier-build.svg\","}, {"file": "src/components/pricing/tier-cards.tsx", "line": 27, "snippet": "icon: \"/placeholders/img/tier-build.svg\","}], "suggestedPrompt": "GPT-5 IMAGE: Minimal line icon: expanding radial lines from centre. Monoline, geometric, export as clean SVG."}, {"id": "public/placeholders/img/tier-pro.svg", "path": "public/placeholders/img/tier-pro.svg", "type": "icon", "exists": true, "referencedIn": [{"file": "src/components/pricing/pricing-cards.tsx", "line": 51, "snippet": "icon: \"/placeholders/img/tier-pro.svg\","}, {"file": "src/components/pricing/tier-cards.tsx", "line": 44, "snippet": "icon: \"/placeholders/img/tier-pro.svg\","}], "suggestedPrompt": "GPT-5 IMAGE: Minimal line icon: stacked cubes around centre point. Monoline, geometric, export as clean SVG."}, {"id": "public/placeholders/img/tier-start.svg", "path": "public/placeholders/img/tier-start.svg", "type": "icon", "exists": true, "referencedIn": [{"file": "src/components/pricing/pricing-cards.tsx", "line": 14, "snippet": "icon: \"/placeholders/img/tier-start.svg\","}, {"file": "src/components/pricing/tier-cards.tsx", "line": 12, "snippet": "icon: \"/placeholders/img/tier-start.svg\","}], "suggestedPrompt": "GPT-5 IMAGE: Minimal line icon: lightning on an axis. Monoline, geometric, export as clean SVG."}]