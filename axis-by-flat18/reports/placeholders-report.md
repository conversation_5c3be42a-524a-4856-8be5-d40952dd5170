# Axis — Placeholder Assets Report

| Asset | Type | Exists | References | Suggested Prompt |
|---|---|---|---|---|
| `public/placeholders/README.md` | other | ✅ | 0 | GPT-5 IMAGE: Minimalist, dark, premium visual matching Axis palette. No text. |
| `public/placeholders/img/tier-build.svg` | icon | ✅ | 2 | GPT-5 IMAGE: Minimal line icon: expanding radial lines from centre. Monoline, geometric, export as clean SVG. |
| `public/placeholders/img/tier-pro.svg` | icon | ✅ | 2 | GPT-5 IMAGE: Minimal line icon: stacked cubes around centre point. Monoline, geometric, export as clean SVG. |
| `public/placeholders/img/tier-start.svg` | icon | ✅ | 2 | GPT-5 IMAGE: Minimal line icon: lightning on an axis. Monoline, geometric, export as clean SVG. |

## public/placeholders/README.md

**Type:** other • **Exists:** Yes
**Suggested Prompt:** GPT-5 IMAGE: Minimalist, dark, premium visual matching Axis palette. No text.

## public/placeholders/img/tier-build.svg

**Type:** icon • **Exists:** Yes
**Suggested Prompt:** GPT-5 IMAGE: Minimal line icon: expanding radial lines from centre. Monoline, geometric, export as clean SVG.

| File | Line | Component | Alt | Snippet |
|---|---:|---|---|---|
| `src/components/pricing/pricing-cards.tsx` | 32 |  |  | `icon: "/placeholders/img/tier-build.svg",` |
| `src/components/pricing/tier-cards.tsx` | 27 |  |  | `icon: "/placeholders/img/tier-build.svg",` |

## public/placeholders/img/tier-pro.svg

**Type:** icon • **Exists:** Yes
**Suggested Prompt:** GPT-5 IMAGE: Minimal line icon: stacked cubes around centre point. Monoline, geometric, export as clean SVG.

| File | Line | Component | Alt | Snippet |
|---|---:|---|---|---|
| `src/components/pricing/pricing-cards.tsx` | 51 |  |  | `icon: "/placeholders/img/tier-pro.svg",` |
| `src/components/pricing/tier-cards.tsx` | 44 |  |  | `icon: "/placeholders/img/tier-pro.svg",` |

## public/placeholders/img/tier-start.svg

**Type:** icon • **Exists:** Yes
**Suggested Prompt:** GPT-5 IMAGE: Minimal line icon: lightning on an axis. Monoline, geometric, export as clean SVG.

| File | Line | Component | Alt | Snippet |
|---|---:|---|---|---|
| `src/components/pricing/pricing-cards.tsx` | 14 |  |  | `icon: "/placeholders/img/tier-start.svg",` |
| `src/components/pricing/tier-cards.tsx` | 12 |  |  | `icon: "/placeholders/img/tier-start.svg",` |
