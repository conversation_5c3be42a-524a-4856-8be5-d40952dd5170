# Axis — Sora & GPT-5 Asset Generation Directives

 > Generate assets to replace all placeholders. Keep British English in any alt text.

----
## public/placeholders/README.md
**Type:** other
**Brand palette:** #0D1117 (base), #E6EDF3 (text), #2F81F7, #9D4BFF, #00E0B8.
**GPT-5 IMAGE PROMPT:**
GPT-5 IMAGE: Minimalist, dark, premium visual matching Axis palette. No text.
**Format:** PNG/WEBP, 2x DPR, ~1600px width for hero/mockups; keep file sizes lean.
**Alt text suggestion:** Keep concise, e.g., “Axis hero animation loop” / “Axis tier icon — Start”.

----
## public/placeholders/img/tier-build.svg
**Type:** icon
**Brand palette:** #0D1117 (base), #E6EDF3 (text), #2F81F7, #9D4BFF, #00E0B8.
**Used in:** `src/components/pricing/pricing-cards.tsx:32`, `src/components/pricing/tier-cards.tsx:27`
**GPT-5 IMAGE PROMPT:**
GPT-5 IMAGE: Minimal line icon: expanding radial lines from centre. Monoline, geometric, export as clean SVG.
**Format:** Clean SVG, monoline strokes, 24x24 or 48x48 artboard.
**Alt text suggestion:** Keep concise, e.g., “Axis hero animation loop” / “Axis tier icon — Start”.

----
## public/placeholders/img/tier-pro.svg
**Type:** icon
**Brand palette:** #0D1117 (base), #E6EDF3 (text), #2F81F7, #9D4BFF, #00E0B8.
**Used in:** `src/components/pricing/pricing-cards.tsx:51`, `src/components/pricing/tier-cards.tsx:44`
**GPT-5 IMAGE PROMPT:**
GPT-5 IMAGE: Minimal line icon: stacked cubes around centre point. Monoline, geometric, export as clean SVG.
**Format:** Clean SVG, monoline strokes, 24x24 or 48x48 artboard.
**Alt text suggestion:** Keep concise, e.g., “Axis hero animation loop” / “Axis tier icon — Start”.

----
## public/placeholders/img/tier-start.svg
**Type:** icon
**Brand palette:** #0D1117 (base), #E6EDF3 (text), #2F81F7, #9D4BFF, #00E0B8.
**Used in:** `src/components/pricing/pricing-cards.tsx:14`, `src/components/pricing/tier-cards.tsx:12`
**GPT-5 IMAGE PROMPT:**
GPT-5 IMAGE: Minimal line icon: lightning on an axis. Monoline, geometric, export as clean SVG.
**Format:** Clean SVG, monoline strokes, 24x24 or 48x48 artboard.
**Alt text suggestion:** Keep concise, e.g., “Axis hero animation loop” / “Axis tier icon — Start”.
