# Axis by Flat 18 — Business Plan
**Date:** 27 August 2025  
**Tagline:** The centre point for rapid MVPs and apps.

## Executive Summary
Axis by Flat 18 delivers affordable, fast MVPs and small apps with premium polish. We use AI‑accelerated development behind the scenes, but we sell outcomes: credible demos, clean UX, and production‑capable pilots. Axis targets lower‑cost projects than Flat 18 usually undertakes, preserving Flat 18’s premium status for complex systems.

## Brand & Positioning
- **Name:** Axis by Flat 18  
- **Value Proposition:** Speed with structure — rapid delivery anchored in Flat 18’s standards.  
- **Voice:** Crisp, confident, geometric/architectural metaphors.
- **Palette:** Base #0D1117 • Text #E6EDF3 • A1 #2F81F7 • A2 #9D4BFF • A3 #00E0B8 • Support #FF6B6B
- **Type:** Space Grotesk (display), Inter (body)

## Target Audience (ICP)
1. Seed‑Stage Founders — investor‑ready MVPs in weeks  
2. Crypto & DeFi Teams — wallets, dashboards, tokens  
3. Innovation Labs — production pilots, not slides

## Offer & Pricing
- **Axis Start — £1,495 (1 week):** LP/microsite/single‑feature MVP; 1 revision  
- **Axis Build — £2,995 (2–3 weeks):** MVP app + up to 2 integrations; auth; 2 revisions  
- **Axis Pro — £5,495 (4–5 weeks):** Complex apps/dashboards; up to 4 integrations; 2 revisions  
- **Add‑On:** Axis Scale Support — £750/mo (minor improvements, fixes, light features)

## Website & Build Directive
- Next.js 14 + TS • Tailwind + shadcn • Framer Motion • Umami  
- Routes: /, /work, /process, /start, /legal  
- Components: Hero, TierCards, WhyAxis, ProcessTimeline, AudienceTiles, FeaturedWork, PricingCards, FinalCTA, LeadForm  
- SEO: Title/Description, JSON‑LD (Org + Product), OG image  
- Assets (placeholders): hero loop, tier icons, 3 mockups with alt text

### Motion & Visual Prompts
- **Hero:** 10s loop — dark geometric grid rotating around a central axis; electric blue/violet sweeps; no text  
- **Breakers:** 4s gradient waves  
- **Icons:** Start (lightning on axis), Build (radials), Pro (stacked cubes)  
- **Mockups:** dashboard, MVP landing, token table + charts

## Go‑To‑Market
- **Position:** Affordable, fast builds with premium polish  
- **Offers:** 48‑hour Axis slot • fixed price/time sprints  
- **Channels:** Founder/DeFi communities, accelerators, technical content  
- **Lead Flow:** /start → Slack/Notion → 20‑min scoping → pay‑link → slot

## Operations
- **Process:** Kickoff → Design → Build → Launch  
- **Guardrails:** tokens, component library, QA checklist  
- **Commercials:** 50% deposit • 50% pre‑launch • simple MSA • one add‑on only

## Financial Model (Illustrative)
| Scenario | Monthly Revenue (£) | Monthly COGS (£) | Monthly Gross Profit (£) | Monthly Opex (£) | Monthly EBITDA (£) | Annual Revenue (£) | Annual EBITDA (£) |
|---|---:|---:|---:|---:|---:|---:|---:|
| Conservative | 16,222 | 2,695 | 13,528 | 1,000 | 12,528 | 194,670 | 150,330 |
| Base | 27,955 | 4,567 | 23,388 | 1,000 | 22,388 | 335,460 | 268,659 |
| Upside | 49,175 | 8,086 | 41,089 | 1,000 | 40,089 | 590,100 | 481,068 |

## Risks & Mitigation
- Perception of "cheap" → premium design, strict scope, Flat 18 credibility  
- Scope creep → fixed timebox, backlog for next sprint or Scale Support  
- AI fragility → human QA + coding standards  
- Capacity peaks → vetted contractor bench + calendar slots  
- Security/compliance → escalate to Flat 18 for enterprise work

## 90‑Day Roadmap & KPIs
**0–30:** Launch site, publish 3 case minis + 2 build diaries, run 48‑hour slot campaign  
**31–60:** 6–8 paid sprints, formalise contractor bench, 4 Scale Support clients  
**61–90:** Refine library, 2 technical deep‑dives, live Axis → Flat 18 upgrade path

**KPIs:** Monthly bookings (count/£), delivery time, gross margin per tier, lead→close %, clients on Scale Support

---
**Domain:** axis-web.dev (primary); axisbyflat18.com/.co.uk (defensive) • Hosting: Vercel • Analytics: Umami • © 2025 Flat 18
