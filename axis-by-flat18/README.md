# Axis by Flat 18

The centre point for rapid MVPs and apps. A high-converting, high-performance website built with Next.js 14, TypeScript, TailwindCSS, and shadcn-ui.

## Features

- **Fixed-Scope Services**: Three tier structure (Start, Build, Pro) with clear pricing
- **Responsive Design**: Mobile-first approach with dark theme
- **Performance Optimized**: Built for 95+ Lighthouse scores
- **SEO Ready**: Complete metadata, sitemap, robots.txt, and JSON-LD structured data
- **Accessibility**: WCAG 2.1 AA compliant with proper focus states and semantic markup
- **Lead Capture**: Integrated form with Postgres (Neon) support and fallback logging
- **Analytics**: Umami integration with dev environment feature flag

## Tech Stack

- **Framework**: Next.js 14 (App Router)
- **Language**: TypeScript
- **Styling**: TailwindCSS + shadcn-ui components
- **Animation**: Framer Motion
- **Forms**: React Hook Form + Zod validation
- **Database**: Postgres (Neon)
- **Analytics**: Umami
- **Deployment**: Vercel

## Measurement

- Capture a two-week baseline before experimenting: visits, `Start a project` and `Book a 15-minute call` click-throughs, and intake form completions.
- No split-testing or variant experiments until that baseline has been reviewed.

## Getting Started

### Prerequisites

- Node.js 18.20.4 or higher
- npm or yarn package manager

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd axis-by-flat18
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.local.example .env.local
```

4. Configure your environment variables in `.env.local`:
```env
# Umami Analytics (optional)
NEXT_PUBLIC_UMAMI_WEBSITE_ID=your-umami-website-id
NEXT_PUBLIC_UMAMI_SRC=https://umami.is/script.js

# Database (Neon / Postgres)
DATABASE_URL=**************************************************

# Chatwoot Live Chat (optional)
NEXT_PUBLIC_CHATWOOT_BASE_URL=https://chat.yourdomain.com
NEXT_PUBLIC_CHATWOOT_WEBSITE_TOKEN=your-chatwoot-website-token

# Invoice Ninja Client Portal (optional)
# Example: https://invoiceninja.yourdomain.com/client/login or hosted portal URL
NEXT_PUBLIC_INVOICE_NINJA_PORTAL_URL=https://invoiceninja.yourdomain.com/client/login

# Google Analytics (optional)
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXXX

# Booking (optional)
# Use either an inline embed or an external link
# Example Cal.com embed URL: https://cal.com/your-handle/intro?embed=true
NEXT_PUBLIC_BOOKING_EMBED_URL=
# Example: https://cal.com/your-handle/intro
NEXT_PUBLIC_BOOKING_LINK=

# Invoice Ninja (subscriptions / invoices)
# Base host for API and portal
IN_HOST_URL=https://invoiceninja.yourdomain.com
IN_API_TOKEN=your-in-api-token

# Public subscription links (for subscription mode)
IN_SUB_START_URL=https://invoiceninja.yourdomain.com/public/subscriptions/axis-start
IN_SUB_BUILD_URL=https://invoiceninja.yourdomain.com/public/subscriptions/axis-build
IN_SUB_PRO_URL=https://invoiceninja.yourdomain.com/public/subscriptions/axis-pro

# Tier amounts for invoice flow (invoice mode)
IN_TIER_START_AMOUNT=1495
IN_TIER_BUILD_AMOUNT=2995
IN_TIER_PRO_AMOUNT=5495
```

### Development

Start the development server:
```bash
npm run dev
```

The website will be available at `http://localhost:3000`.

### Building for Production

1. Build the application:
```bash
npm run build
```

2. Start the production server:
```bash
npm start
```

### Type Checking

Run TypeScript type checking:
```bash
npm run type-check
```

### Linting

Run ESLint:
```bash
npm run lint
```

## Project Structure

```
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── api/               # API routes
│   │   ├── legal/             # Legal documentation page
│   │   ├── process/           # Process methodology page
│   │   ├── start/             # Lead capture form page
│   │   ├── work/              # Case studies page
│   │   ├── layout.tsx         # Root layout
│   │   └── page.tsx           # Homepage
│   ├── components/            # React components
│   │   ├── ui/                # shadcn-ui components
│   │   ├── hero/              # Hero section components
│   │   ├── pricing/           # Pricing components
│   │   ├── forms/             # Form components
│   │   └── ...                # Other feature components
│   └── lib/                   # Utility functions
├── public/                    # Static assets
│   └── placeholders/          # Placeholder images and videos
├── tailwind.config.js         # TailwindCSS configuration
├── tsconfig.json              # TypeScript configuration
└── next.config.ts             # Next.js configuration
```

## Configuration

### Onboarding Paths

- **Submit Project**: `/start` — lead form posts to `/api/lead`.
- **Subscribe & Self‑Onboard**: `/onboard` → opens `NEXT_PUBLIC_INVOICE_NINJA_PORTAL_URL`.
- **Live Chat**: Chatwoot widget loads globally when `NEXT_PUBLIC_CHATWOOT_BASE_URL` and `NEXT_PUBLIC_CHATWOOT_WEBSITE_TOKEN` are set. `/onboard` provides a “Start Live Chat” button.

### Invoice Ninja Hybrid Router

- API: `POST /api/invoiceninja/subscribe` with `{ name?, email, tier: 'start'|'build'|'pro', mode?: 'subscription'|'invoice', utm? }`.
- Behavior:
  - If `mode='subscription'` and `IN_SUB_*_URL` is set → returns `{ redirectUrl }` to public subscription.
  - Else uses `IN_HOST_URL` + `IN_API_TOKEN` to find/create a client, create an invoice with the tier amount, and returns an invitation link `{ redirectUrl }`.
  - Passes UTM into client/invoice notes when provided.

### Invoice Ninja Webhook → Database Sync

- Endpoint: `POST /api/invoiceninja/webhook`
- Optional auth: set `IN_WEBHOOK_SECRET` and configure Invoice Ninja to send header `X-IN-Webhook-Secret: <secret>`.
- Behavior:
  - Logs raw event to `in_events` table (if exists) with columns: `id (uuid)`, `event_type text`, `data jsonb`, `created_at timestamptz default now()`.
  - Attempts to upsert client into `clients` by email: columns suggested → `id uuid`, `external_id text`, `email text unique`, `name text`, `source text`, `created_at timestamptz`.
  - Attempts to upsert invoice/payment into `payments` by `external_id`: columns suggested → `id uuid`, `external_id text unique`, `reference text`, `amount numeric`, `status text`, `provider text`, `raw jsonb`, `created_at timestamptz`.
- Example SQL (optional):
```sql
create table if not exists in_events (
  id uuid primary key default gen_random_uuid(),
  event_type text,
  data jsonb,
  created_at timestamptz default now()
);

create table if not exists clients (
  id uuid primary key default gen_random_uuid(),
  external_id text,
  email text unique,
  name text,
  source text,
  created_at timestamptz default now()
);

create table if not exists payments (
  id uuid primary key default gen_random_uuid(),
  external_id text unique,
  reference text,
  amount numeric,
  status text,
  provider text,
  raw jsonb,
  created_at timestamptz default now()
);
```

### Notifications

- Slack (optional): set `SLACK_WEBHOOK_URL` to receive formatted notifications when payments are recorded.
- Chatwoot (optional): set `CHATWOOT_API_BASE`, `CHATWOOT_ACCOUNT_ID`, `CHATWOOT_INBOX_ID`, `CHATWOOT_API_TOKEN` to create or update a conversation and post a payment message.

### Admin Dashboard (basic)

- A minimal internal dashboard is available at `/admin?token=YOUR_TOKEN`.
- Set `ADMIN_DASHBOARD_TOKEN` in your environment to protect access.
- Shows recent leads and payments (reads directly from Postgres).

### Umami Analytics

To enable analytics:

1. Create an Umami account and website
2. Add your website ID to `NEXT_PUBLIC_UMAMI_WEBSITE_ID`
3. Analytics are automatically disabled in development

### Ackee Analytics

Optional privacy-friendly analytics via Ackee. To enable:

1. Deploy or use an existing Ackee server
2. Set the following env vars:
   - `NEXT_PUBLIC_ACKEE_SERVER` (e.g. `https://your-ackee-server`)
   - `NEXT_PUBLIC_ACKEE_DOMAIN_ID` (your Ackee domain/site id)
   - `NEXT_PUBLIC_ACKEE_SRC` (optional script URL override)
3. The script loads only in production and only when both server and domain id are set

### Lead Form Database

The lead form supports two modes:

1. **Postgres (Neon)** (recommended for production):
   - Configure `DATABASE_URL`
   - Create a `leads` table with the following schema:
   ```sql
   CREATE TABLE leads (
     id SERIAL PRIMARY KEY,
     name TEXT NOT NULL,
     email TEXT NOT NULL,
     company TEXT,
     idea TEXT NOT NULL,
     tier TEXT NOT NULL,
     timeline TEXT NOT NULL,
     budget TEXT NOT NULL,
     addon TEXT,
     source TEXT DEFAULT 'website',
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
   );
   ```

2. **Console Logging** (fallback):
   - If `DATABASE_URL` is not configured, form submissions will be logged to the console
   - Suitable for development and testing

## Brand Guidelines

### Colors
- **Base**: `#0D1117` (axis-base)
- **Text**: `#E6EDF3` (axis-text)
- **Accent 1**: `#2F81F7` (axis-accent-1) - Primary blue
- **Accent 2**: `#9D4BFF` (axis-accent-2) - Purple
- **Accent 3**: `#00E0B8` (axis-accent-3) - Teal
- **Support**: `#FF6B6B` (axis-support) - Red for errors

### Typography
- **Display**: Space Grotesk (headings, titles)
- **Body**: Inter (body text, UI elements)

### Voice & Tone
- British English
- Professional but approachable
- Crisp and confident
- Geometric/architectural metaphors

## Booking & Anonymous Briefs

- Anonymous, text‑first briefs are supported via Chatwoot. Set:
  - `NEXT_PUBLIC_CHATWOOT_BASE_URL`
  - `NEXT_PUBLIC_CHATWOOT_WEBSITE_TOKEN`
  The Start page features a “Start an anonymous brief” button that opens the chat widget without collecting an email.

- Calls can be scheduled via an inline embed or external link:
  - Set `NEXT_PUBLIC_BOOKING_EMBED_URL` (e.g., Cal.com/Calendly embed URL) for an inline calendar on the Start page.
  - Or set `NEXT_PUBLIC_BOOKING_LINK` to show a button that opens your booking page in a new tab.

## Deployment

### Vercel (Recommended)

1. Connect your GitHub repository to Vercel
2. Set environment variables in the Vercel dashboard
3. Deploy automatically on push to main branch

### Manual Deployment

1. Build the application:
```bash
npm run build
```

2. Deploy the `.next` folder and `public` directory to your hosting provider

## SEO Features

- **Metadata**: Complete page metadata for all routes
- **Open Graph**: Social media sharing optimization
- **Twitter Cards**: Twitter sharing optimization
- **JSON-LD**: Structured data for organization, products, and services
- **Sitemap**: Auto-generated sitemap.xml
- **Robots.txt**: Search engine crawling instructions

## Accessibility

The website meets WCAG 2.1 AA standards with:

- Semantic HTML markup
- Proper heading hierarchy
- Keyboard navigation support
- Focus indicators
- Screen reader compatibility
- Color contrast compliance
- Alternative text for images
- Reduced motion support

## Performance

Optimized for Core Web Vitals:

- Image optimization with Next.js `<Image />` component
- Code splitting and lazy loading
- Minimal JavaScript bundle size
- Efficient CSS with TailwindCSS
- Prefetching for critical user journeys

## License

© 2025 Flat 18. All rights reserved.

## Support

For questions or support regarding this codebase, please contact the Flat 18 development team.

---

**Built with ❤️ by [Flat 18](https://flat18.co.uk)**
