import { getSql, withRetry } from "@/lib/db";

export default async function AdminPage({ searchParams }: { searchParams: { token?: string } }) {
  const token = process.env.ADMIN_DASHBOARD_TOKEN;
  const provided = searchParams?.token;
  if (!token || provided !== token) {
    return (
      <main className="container mx-auto px-4 py-16">
        <h1 className="text-2xl font-semibold">Unauthorized</h1>
        <p className="text-sm text-muted-foreground mt-2">Provide a valid token to view dashboard.</p>
      </main>
    );
  }

  const sql = getSql();
  const leads = (await withRetry(() => sql`
    select id,name,email,tier,created_at from leads order by created_at desc limit 25
  `)) as { id: number; name: string; email: string; tier: string; created_at: string }[];
  const pays = (await withRetry(() => sql`
    select external_id,reference,amount,status,email,created_at from payments order by created_at desc limit 25
  `)) as { external_id: string | null; reference: string | null; amount: number | null; status: string | null; email: string | null; created_at: string }[];

  return (
    <main className="container mx-auto px-4 py-16">
      <h1 className="text-3xl font-semibold mb-6">Admin Dashboard</h1>
      <div className="grid gap-8 lg:grid-cols-2">
        <section>
          <h2 className="text-xl font-semibold mb-3">Recent Leads</h2>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="text-left border-b border-border/50">
                  <th className="py-2 pr-4">Name</th>
                  <th className="py-2 pr-4">Email</th>
                  <th className="py-2 pr-4">Tier</th>
                  <th className="py-2">Created</th>
                </tr>
              </thead>
              <tbody>
                {leads?.map((l) => (
                  <tr key={l.id} className="border-b border-border/30">
                    <td className="py-2 pr-4">{l.name}</td>
                    <td className="py-2 pr-4">{l.email}</td>
                    <td className="py-2 pr-4">{l.tier}</td>
                    <td className="py-2">{new Date(l.created_at).toLocaleString()}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </section>
        <section>
          <h2 className="text-xl font-semibold mb-3">Recent Payments</h2>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="text-left border-b border-border/50">
                  <th className="py-2 pr-4">Ref</th>
                  <th className="py-2 pr-4">Email</th>
                  <th className="py-2 pr-4">Amount</th>
                  <th className="py-2 pr-4">Status</th>
                  <th className="py-2">Created</th>
                </tr>
              </thead>
              <tbody>
                {pays?.map((p) => (
                  <tr key={p.external_id} className="border-b border-border/30">
                    <td className="py-2 pr-4">{p.reference || p.external_id}</td>
                    <td className="py-2 pr-4">{p.email || '-'}</td>
                    <td className="py-2 pr-4">{p.amount ?? '-'}</td>
                    <td className="py-2 pr-4">{p.status}</td>
                    <td className="py-2">{new Date(p.created_at).toLocaleString()}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </section>
      </div>
    </main>
  );
}
