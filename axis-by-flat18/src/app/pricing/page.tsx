import Link from "next/link";

import { Header } from "@/components/header";
import { Footer } from "@/components/footer";
import { PricingCards } from "@/components/pricing/pricing-cards";
import { Button } from "@/components/ui/button";

const comparisonRows = [
  {
    label: "Deliverables",
    start: "Landing / proof screens",
    build: "Full MVP journeys",
    pro: "Multi-surface + admin",
  },
  {
    label: "Integrations",
    start: "1",
    build: "Up to 3",
    pro: "Up to 6",
  },
  {
    label: "Analytics & monitoring",
    start: "Basic",
    build: "Enhanced",
    pro: "Advanced",
  },
  {
    label: "Launch support",
    start: "Handover + checklist",
    build: "Launch partner + monitoring",
    pro: "Launch partner + stakeholder enablement",
  },
  {
    label: "Post-launch",
    start: "30-day stabilisation",
    build: "30-day stabilisation",
    pro: "30-day stabilisation + transition plan",
  },
];

const faqs = [
  {
    question: "Can we upgrade between tiers?",
    answer: "Yes. We scope the delta and apply the difference. Many teams begin on Start and move to Build once they see traction.",
  },
  {
    question: "What does fixed scope mean?",
    answer: "We agree the features, integrations, and launch checklist up front. Decisions go in the workspace so there are no surprises.",
  },
  {
    question: "How do payments work?",
    answer: "50% deposit to book the slot. 50% due prior to launch. Scale Support is billed monthly in advance.",
  },
  {
    question: "Do you offer retainers?",
    answer: "Axis focuses on fixed-scope builds and scale support. Larger retainers run through Flat 18 once the MVP is live.",
  },
];

export default function PricingPage() {
  return (
    <div className="min-h-screen bg-white text-slate-900">
      <Header />
      <main id="main" className="bg-white">
        <section className="bg-gradient-to-br from-[#eef5ff] to-white py-20">
          <div className="mx-auto max-w-[1200px] px-4 md:px-6">
            <p className="text-xs font-semibold uppercase tracking-[0.3em] text-slate-400">
              Pricing
            </p>
            <div className="mt-6 grid gap-10 lg:grid-cols-[2fr,1fr]">
              <div className="space-y-6">
                <h1 className="font-display text-4xl font-semibold leading-tight sm:text-5xl">
                  Fixed pricing structured around real MVP launch patterns.
                </h1>
                <p className="max-w-2xl text-lg text-slate-600">
                  Three tiers cover the common scopes we see. Need something custom? We’ll advise during scoping and map the path into Flat 18 when necessary.
                </p>
                <div className="flex flex-col gap-3 sm:flex-row">
                  <Button asChild className="bg-[var(--primary)] text-white hover:bg-[#266ee6]">
                    <Link href="/start">Start a project</Link>
                  </Button>
                  <Button asChild variant="ghost" className="text-slate-700 hover:bg-slate-100">
                    <Link href="/call">Talk about scope</Link>
                  </Button>
                </div>
              </div>
              <div className="space-y-3 rounded-3xl border border-slate-200 bg-white p-6 shadow-lg shadow-slate-900/10">
                <p className="text-sm font-semibold uppercase tracking-[0.3em] text-slate-400">
                  Included by default
                </p>
                <ul className="space-y-3 text-sm text-slate-600">
                  <li>Weekly demos and async updates</li>
                  <li>QA budget for accessibility & performance</li>
                  <li>Launch checklist + runbooks + 30-day support</li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        <PricingCards />

        <section className="bg-white py-16">
          <div className="mx-auto max-w-[1200px] px-4 md:px-6">
            <div className="mb-8 text-center">
              <h2 className="font-display text-3xl font-semibold text-slate-900 sm:text-4xl">
                Compare inclusions at a glance
              </h2>
            </div>
            <div className="overflow-hidden rounded-3xl border border-slate-200">
              <table className="w-full text-left text-sm text-slate-600">
                <thead className="bg-slate-50 text-xs uppercase tracking-[0.3em] text-slate-500">
                  <tr>
                    <th className="px-6 py-4">Area</th>
                    <th className="px-6 py-4">Start</th>
                    <th className="px-6 py-4">Build</th>
                    <th className="px-6 py-4">Pro</th>
                  </tr>
                </thead>
                <tbody>
                  {comparisonRows.map((row) => (
                    <tr key={row.label} className="odd:bg-white even:bg-slate-50">
                      <th scope="row" className="px-6 py-4 text-slate-900">{row.label}</th>
                      <td className="px-6 py-4">{row.start}</td>
                      <td className="px-6 py-4">{row.build}</td>
                      <td className="px-6 py-4">{row.pro}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </section>

        <section className="bg-slate-50 py-16" id="faq">
          <div className="mx-auto max-w-[1200px] px-4 md:px-6">
            <div className="mb-10 text-center">
              <h2 className="font-display text-3xl font-semibold text-slate-900 sm:text-4xl">
                Pricing FAQs
              </h2>
              <p className="mt-3 text-lg text-slate-600">Clarity before we begin. Ask us if something isn’t covered here.</p>
            </div>
            <div className="space-y-4">
              {faqs.map((faq) => (
                <details key={faq.question} className="rounded-3xl border border-slate-200 bg-white p-6 shadow-sm shadow-slate-900/5">
                  <summary className="cursor-pointer text-lg font-semibold text-slate-900">
                    {faq.question}
                  </summary>
                  <p className="mt-3 text-sm text-slate-600">{faq.answer}</p>
                </details>
              ))}
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
}
