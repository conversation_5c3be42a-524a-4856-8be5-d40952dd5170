@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Neutrals (dark-first palette) */
    --bg-950: #0b0f14;
    --bg-900: #0f1520;
    --bg-800: #131b26;
    --surface-700: #162131;
    --border-600: #1e2a3a;

    /* Text tokens */
    --text-100: #eaf2ff;
    --text-300: #c0cee6;
    --text-500: #8fa3c2;

    /* Brand accents */
    --axis-cobalt: #2f81f7;
    --axis-secondary: #4ea1f5;
    --axis-teal: #00e0b8;
    --axis-purple: #9d4bff;

    /* Feedback states */
    --success: #21c36f;
    --warning: #ffb020;
    --danger: #ff4d4d;

    /* Radii & elevation */
    --radius-lg: 14px;
    --radius-md: 10px;
    --radius-sm: 8px;
    --elev-1: 0 6px 24px rgba(0, 0, 0, 0.18);
    --elev-2: 0 12px 36px rgba(0, 0, 0, 0.24);
    --shadow-sm: 0 2px 12px rgba(9, 14, 26, 0.18);
    --shadow-md: var(--elev-1);
    --shadow-lg: var(--elev-2);

    /* Legacy bridges */
    --bg: var(--bg-900);
    --panel: var(--surface-700);
    --panel-strong: #1c2737;
    --text: var(--text-100);
    --muted: var(--text-300);
    --text-soft: rgba(234, 242, 255, 0.82);
    --text-subtle: rgba(234, 242, 255, 0.64);
    --primary: var(--axis-cobalt);
    --link: var(--axis-cobalt);
    --cta: var(--axis-secondary);
    --focus: var(--warning);
    --border: var(--border-600);
    --border-strong: #243244;
    --axis-base: var(--bg);
    --axis-text: var(--text);
    --axis-accent-1: var(--axis-cobalt);
    --axis-accent-2: var(--axis-purple);
    --axis-accent-3: var(--axis-secondary);

    /* Tailwind HSL bridges */
    --background: 216 45% 9%;
    --foreground: 213 100% 97%;
    --card: 217 35% 14%;
    --card-foreground: 213 100% 94%;
    --popover: 217 35% 14%;
    --popover-foreground: 213 100% 94%;
    --primary-hsl: 217 91% 58%;
    --primary-foreground: 210 40% 98%;
    --secondary-hsl: 166 100% 44%;
    --secondary-foreground: 217 45% 10%;
    --muted-hsl: 214 32% 52%;
    --muted-foreground: 214 34% 78%;
    --accent-hsl: 166 100% 44%;
    --accent-foreground: 217 45% 10%;
    --destructive-hsl: 0 78% 63%;
    --destructive-foreground: 210 40% 98%;
    --ring-hsl: 217 91% 58%;
    --radius: var(--radius-md);
  }

  :root[data-theme="dark"],
  .dark {
    --bg-950: #0b0f14;
    --bg-900: #0f1520;
    --bg-800: #131b26;
    --surface-700: #162131;
    --border-600: #1e2a3a;
    --text-100: #eaf2ff;
    --text-300: #c0cee6;
    --text-500: #8fa3c2;
    --axis-cobalt: #5ea3ff;
    --axis-teal: #35e7cc;
    --axis-purple: #b694ff;
    --success: #21c36f;
    --warning: #ffc155;
    --danger: #ff6666;
    --panel-strong: #1c2737;
    --text-soft: rgba(234, 242, 255, 0.82);
    --text-subtle: rgba(234, 242, 255, 0.64);
    --primary: var(--axis-cobalt);
    --link: var(--axis-cobalt);
    --cta: var(--axis-secondary);
    --focus: var(--warning);
    --border: var(--border-600);
    --border-strong: #243244;
    --axis-base: var(--bg-900);
    --axis-text: var(--text-100);
    --axis-accent-1: var(--axis-cobalt);
    --axis-accent-2: var(--axis-purple);
    --axis-accent-3: var(--axis-secondary);
    --background: 216 45% 9%;
    --foreground: 213 100% 97%;
    --card: 217 35% 14%;
    --card-foreground: 213 100% 94%;
    --popover: 217 35% 14%;
    --popover-foreground: 213 100% 94%;
    --primary-hsl: 217 86% 66%;
    --primary-foreground: 213 35% 12%;
    --secondary-hsl: 166 90% 54%;
    --secondary-foreground: 213 35% 12%;
    --muted-hsl: 214 28% 56%;
    --muted-foreground: 214 34% 78%;
    --accent-hsl: 166 90% 54%;
    --accent-foreground: 213 35% 12%;
    --destructive-hsl: 0 78% 63%;
    --destructive-foreground: 213 35% 12%;
    --ring-hsl: 217 86% 66%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground antialiased;
    background-color: var(--bg);
    color: var(--text);
    font-feature-settings: "liga" 1, "kern" 1;
  }

  a {
    color: var(--link);
    transition: color 150ms ease;
  }

  a:hover,
  a:focus-visible {
    color: #fff;
  }

  ::selection {
    background: rgba(47, 129, 247, 0.2);
    color: var(--text);
  }

  :focus-visible {
    outline: 2px solid var(--focus);
    outline-offset: 2px;
  }
}

@layer utilities {
  @keyframes hero-rotate {
    from {
      transform: rotate(0deg) scale(3);
    }

    to {
      transform: rotate(360deg) scale(3);
    }
  }

  .animate-hero-rotate {
    animation: hero-rotate 36s linear infinite;
    transform-origin: center;
  }
}

@layer components {
  .skip-link {
    position: absolute;
    left: 1rem;
    top: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--primary);
    color: #0b1220;
    border-radius: 0.75rem;
    transform: translateY(-150%);
    transition: transform 160ms ease;
    z-index: 999;
  }

  .skip-link:focus-visible {
    transform: translateY(0);
  }

  .legal-content {
    @apply max-w-none;
  }

  .legal-content h1 {
    @apply text-3xl lg:text-4xl font-display font-bold mb-6 scroll-mt-24;
    color: var(--text);
  }

  .legal-content h2 {
    @apply text-2xl lg:text-3xl font-display font-bold mt-8 mb-6 scroll-mt-24;
    color: var(--text);
  }

  .legal-content h3 {
    @apply text-xl lg:text-2xl font-display font-bold mt-6 mb-4 scroll-mt-24;
    color: var(--text);
  }

  .legal-content h4 {
    @apply text-lg lg:text-xl font-display font-bold mt-6 mb-4 scroll-mt-24;
    color: var(--text);
  }

  .legal-content p {
    @apply mb-4 leading-relaxed;
    color: var(--text-soft);
  }

  .legal-content ul {
    @apply list-disc list-inside mb-6 ml-4 space-y-2;
  }

  .legal-content li {
    @apply mb-2 leading-relaxed;
    color: var(--text-subtle);
  }

  .legal-content strong {
    @apply font-semibold;
    color: var(--text);
  }

  .legal-content *:focus-visible {
    outline: 2px solid var(--focus);
    outline-offset: 3px;
    border-radius: 0.25rem;
  }
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

.no-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
