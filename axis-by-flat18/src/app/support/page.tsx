import type { <PERSON><PERSON><PERSON> } from "next";

import { Header } from "@/components/header";
import { Footer } from "@/components/footer";
import { <PERSON><PERSON> } from "@/components/ui/button";

const stabilizationCoverage = [
  "Fixing launch issues surfaced in production",
  "Minor polish and copy tweaks",
  "Monitoring auth, data flows, and payments",
  "Repository, environment, and credential handover",
];

const optionalSupportInclusions = [
  "Bug fixes and small improvements",
  "Dependency updates and security patches",
  "Uptime and error monitoring with reports",
  "Performance tuning for slow endpoints",
  "Light feature work and content updates",
];

const optionalSupportWorkflow = [
  "Submit tasks via support inbox or portal",
  "We triage, estimate, and schedule",
  "Receive concise updates as items progress",
];

const escalations = [
  "Roadmapping and discovery workshops",
  "Design systems and complex UX research",
  "Integrations, data pipelines, and compliance",
  "Dedicated sprints with stakeholder management",
];

const faqs = [
  {
    question: "Do you guarantee response times?",
    answer: "Production issues jump to the front of the queue. If you need contractual SLAs we scope that via Flat 18.",
  },
  {
    question: "Can you support a product you didn’t build?",
    answer: "We review the codebase before we commit. If it’s solid and documented we can take it on.",
  },
  {
    question: "Can we pause support?",
    answer: "Yes. Pause at the end of a billing period and restart later. We keep docs current in the meantime.",
  },
];

export const metadata: Metadata = {
  title: "Support & Aftercare — Axis by Flat 18",
  description: "Learn how Axis keeps your product stable after launch and how Scale Support works when you need ongoing help.",
  openGraph: {
    title: "Support & Aftercare — Axis by Flat 18",
    description: "Learn how Axis keeps your product stable after launch and how Scale Support works when you need ongoing help.",
    type: "website",
    images: [
      {
        url: "/brand/axis-og-2-1200x630.webp",
        width: 1200,
        height: 630,
        alt: "Axis by Flat 18 hero preview",
      },
      {
        url: "/brand/axis-og-2-600x315.webp",
        width: 600,
        height: 315,
        alt: "Axis by Flat 18 hero preview",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Support & Aftercare — Axis by Flat 18",
    description: "Learn how Axis keeps your product stable after launch and how Scale Support works when you need ongoing help.",
    images: ["/brand/axis-og-2-1200x630.webp"],
  },
};

export default function SupportPage() {
  return (
    <div className="min-h-screen bg-white text-slate-900">
      <Header />
      <main id="main" className="bg-white">
        <section className="bg-gradient-to-br from-[#eef5ff] to-white py-20">
          <div className="mx-auto max-w-[1200px] px-4 md:px-6">
            <p className="text-xs font-semibold uppercase tracking-[0.3em] text-slate-400">
              Support & aftercare
            </p>
            <div className="mt-6 grid gap-10 lg:grid-cols-[2fr,1fr]">
              <div className="space-y-6">
                <h1 className="font-display text-4xl font-semibold leading-tight sm:text-5xl">
                  Launch ready—and supported once your product is live.
                </h1>
                <p className="max-w-2xl text-lg text-slate-600">
                  Every Axis project includes a stabilisation window. For teams who want the same crew to stay close, Scale Support gives you a predictable path before transitioning into a larger Flat 18 engagement.
                </p>
                <div className="flex flex-col gap-3 sm:flex-row">
                  <Button asChild className="bg-[var(--primary)] text-white hover:bg-[#266ee6]">
                    <a href="/start?addon=scale-support">Add Scale Support</a>
                  </Button>
                  <Button asChild variant="ghost" className="text-slate-700 hover:bg-slate-100">
                    <a href="/call">Discuss support</a>
                  </Button>
                </div>
              </div>
              <div className="space-y-3 rounded-3xl border border-slate-200 bg-white p-6 shadow-lg shadow-slate-900/10">
                <p className="text-sm font-semibold uppercase tracking-[0.3em] text-slate-400">
                  Stabilisation window
                </p>
                <ul className="space-y-3 text-sm text-slate-600">
                  {stabilizationCoverage.map((item) => (
                    <li key={item} className="flex items-start gap-2">
                      <span className="mt-2 inline-flex h-1.5 w-1.5 rounded-full bg-[#2f81f7]" aria-hidden />
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </section>

        <section className="py-16">
          <div className="mx-auto max-w-[1200px] px-4 md:px-6">
            <div className="grid gap-8 lg:grid-cols-2">
              <div className="rounded-3xl border border-slate-200 bg-white p-8 shadow-sm shadow-slate-900/5">
                <h2 className="text-2xl font-semibold text-slate-900">What Scale Support includes</h2>
                <ul className="mt-6 space-y-3 text-sm text-slate-600">
                  {optionalSupportInclusions.map((item) => (
                    <li key={item} className="flex items-start gap-2">
                      <span className="mt-2 inline-flex h-1.5 w-1.5 rounded-full bg-[#35e7cc]" aria-hidden />
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
                <div className="mt-6 rounded-2xl border border-slate-200 bg-slate-50 p-4 text-sm text-slate-600">
                  £750/month • Cancel anytime with 2 weeks notice
                </div>
              </div>
              <div className="rounded-3xl border border-slate-200 bg-white p-8 shadow-sm shadow-slate-900/5">
                <h2 className="text-2xl font-semibold text-slate-900">How it works</h2>
                <ul className="mt-6 space-y-3 text-sm text-slate-600">
                  {optionalSupportWorkflow.map((item) => (
                    <li key={item} className="flex items-start gap-2">
                      <span className="mt-2 inline-flex h-1.5 w-1.5 rounded-full bg-[#2f81f7]" aria-hidden />
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
                <div className="mt-6 rounded-2xl border border-slate-200 bg-slate-50 p-4 text-sm text-slate-600">
                  We prioritise incidents first, then iterate on the backlog we maintain together.
                </div>
              </div>
            </div>
          </div>
        </section>

        <section className="bg-slate-50 py-16">
          <div className="mx-auto max-w-[1200px] px-4 md:px-6">
            <div className="rounded-3xl border border-slate-200 bg-white p-8 shadow-lg shadow-slate-900/10">
              <h2 className="text-2xl font-semibold text-slate-900">When you’ll graduate into Flat 18</h2>
              <p className="mt-3 text-sm text-slate-600">
                Larger programmes move into the Flat 18 studio. We’ll help scope the transition, align milestones, and maintain momentum.
              </p>
              <ul className="mt-6 flex flex-wrap gap-3 text-sm text-slate-600">
                {escalations.map((item) => (
                  <li key={item} className="flex items-center gap-2 rounded-full bg-slate-100 px-4 py-2">
                    <span className="inline-flex h-2 w-2 rounded-full bg-[#2f81f7]" aria-hidden />
                    {item}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </section>

        <section className="py-16">
          <div className="mx-auto max-w-[1200px] px-4 md:px-6">
            <div className="mb-10 text-center">
              <h2 className="font-display text-3xl font-semibold text-slate-900 sm:text-4xl">
                Support FAQs
              </h2>
            </div>
            <div className="space-y-4">
              {faqs.map((faq) => (
                <details key={faq.question} className="rounded-3xl border border-slate-200 bg-white p-6 shadow-sm shadow-slate-900/5">
                  <summary className="cursor-pointer text-lg font-semibold text-slate-900">
                    {faq.question}
                  </summary>
                  <p className="mt-3 text-sm text-slate-600">{faq.answer}</p>
                </details>
              ))}
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
}
