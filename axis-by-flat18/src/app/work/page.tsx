"use client";

import { useMemo, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { motion } from "framer-motion";
import { ArrowUpRight } from "lucide-react";

import { Header } from "@/components/header";
import { Footer } from "@/components/footer";
import { Button } from "@/components/ui/button";

const caseStudies = [
  {
    title: "Multi-chain treasury tracking",
    summary: "Investor dashboard aggregating DeFi treasury positions with live metrics and alerts.",
    useCase: "Crypto",
    stack: ["Next.js", "Supabase", "Ethers.js"],
    timeline: "3 weeks",
    highlights: [
      "Token and wallet ingestion pipeline",
      "Risk scoring summary for LPs",
      "CSV exports + investor mode",
    ],
    image: "/placeholders/img/mockup-1.png",
  },
  {
    title: "SaaS conversion operating system",
    summary: "Landing, pricing experiments, and waitlist flows powering GTM for a B2B startup.",
    useCase: "SaaS",
    stack: ["Next.js", "Postgres", "Segment"],
    timeline: "2 weeks",
    highlights: [
      "Pricing toggle tests",
      "Waitlist automation into HubSpot",
      "Lifecycle analytics with Segment",
    ],
    image: "/placeholders/img/mockup-2.png",
  },
  {
    title: "Ops automation cockpit",
    summary: "Internal tooling with approvals, audit trails, and automations to replace spreadsheets.",
    useCase: "Operations",
    stack: ["React", "Hasura", "AWS"],
    timeline: "4 weeks",
    highlights: [
      "Role-based access + approvals",
      "Workflow builder",
      "Error monitoring with Sentry",
    ],
    image: "/placeholders/img/mockup-3.png",
  },
  {
    title: "Research insights hub",
    summary: "Knowledge base and publishing workflow for a media venture with subscriber tiers.",
    useCase: "Media",
    stack: ["Next.js", "Stripe", "Notion API"],
    timeline: "3 weeks",
    highlights: [
      "Subscriber paywall + Stripe billing",
      "Editorial workflow",
      "SEO automation + schema",
    ],
    image: "/placeholders/img/mockup-2.png",
  },
];

const useCaseFilters = ["All", "SaaS", "Crypto", "Operations", "Media"] as const;
const timelineFilters = ["Any", "2 weeks", "3 weeks", "4 weeks"] as const;

export default function WorkPage() {
  const [useCase, setUseCase] = useState<typeof useCaseFilters[number]>("All");
  const [timeline, setTimeline] = useState<typeof timelineFilters[number]>("Any");

  const filtered = useMemo(() => {
    return caseStudies.filter((study) => {
      const useCaseMatch = useCase === "All" || study.useCase === useCase;
      const timelineMatch = timeline === "Any" || study.timeline === timeline;
      return useCaseMatch && timelineMatch;
    });
  }, [useCase, timeline]);

  return (
    <div className="min-h-screen bg-white text-slate-900">
      <Header />
      <main id="main" className="bg-white">
        <section className="bg-gradient-to-br from-[#eef5ff] to-white py-20">
          <div className="mx-auto max-w-[1200px] px-4 md:px-6">
            <p className="text-xs font-semibold uppercase tracking-[0.3em] text-slate-400">
              Work
            </p>
            <div className="mt-6 grid gap-10 lg:grid-cols-[2fr,1fr]">
              <div className="space-y-6">
                <h1 className="font-display text-4xl font-semibold leading-tight sm:text-5xl">
                  Library of demo builds, case studies, and production handovers.
                </h1>
                <p className="max-w-2xl text-lg text-slate-600">
                  We publish real examples of how Axis ships. Filter by use case or timeline, then book a call to see how the process translates to your product.
                </p>
                <div className="flex flex-col gap-3 sm:flex-row">
                  <Button asChild className="bg-[var(--primary)] text-white hover:bg-[#266ee6]">
                    <Link href="/start">Start a project</Link>
                  </Button>
                  <Button asChild variant="ghost" className="text-slate-700 hover:bg-slate-100">
                    <Link href="/call">Book a call</Link>
                  </Button>
                </div>
              </div>
              <div className="space-y-4 rounded-3xl border border-slate-200 bg-white p-6 shadow-lg shadow-slate-900/10">
                <h2 className="text-sm font-semibold uppercase tracking-[0.3em] text-slate-400">
                  Filter work
                </h2>
                <div className="space-y-3 text-sm text-slate-600">
                  <div>
                    <p className="font-semibold text-slate-500">Use case</p>
                    <div className="mt-2 flex flex-wrap gap-2">
                      {useCaseFilters.map((filter) => (
                        <button
                          key={filter}
                          type="button"
                          onClick={() => setUseCase(filter)}
                          className={`rounded-full px-4 py-2 text-xs font-semibold transition ${
                            useCase === filter
                              ? "bg-[var(--primary)] text-white"
                              : "bg-slate-100 text-slate-600 hover:bg-slate-200"
                          }`}
                        >
                          {filter}
                        </button>
                      ))}
                    </div>
                  </div>
                  <div>
                    <p className="font-semibold text-slate-500">Timeline</p>
                    <div className="mt-2 flex flex-wrap gap-2">
                      {timelineFilters.map((filter) => (
                        <button
                          key={filter}
                          type="button"
                          onClick={() => setTimeline(filter)}
                          className={`rounded-full px-4 py-2 text-xs font-semibold transition ${
                            timeline === filter
                              ? "bg-[#35e7cc] text-[#0b1220]"
                              : "bg-slate-100 text-slate-600 hover:bg-slate-200"
                          }`}
                        >
                          {filter}
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <section className="py-16">
          <div className="mx-auto max-w-[1200px] px-4 md:px-6">
            <div className="mb-8 flex items-center justify-between">
              <p className="text-sm text-slate-500">
                Showing {filtered.length} of {caseStudies.length} projects
              </p>
              <Link href="/start" className="hidden text-sm font-semibold text-[var(--primary)] hover:text-[#1b66f6] sm:inline-flex items-center gap-2">
                Request a slot
                <ArrowUpRight className="h-4 w-4" aria-hidden />
              </Link>
            </div>
            <div className="grid gap-8 lg:grid-cols-2">
              {filtered.map((study, index) => (
                <motion.article
                  key={study.title}
                  initial={{ opacity: 0, y: 24 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true, amount: 0.3 }}
                  transition={{ duration: 0.4, delay: index * 0.05 }}
                  className="group overflow-hidden rounded-3xl border border-slate-200 bg-white shadow-sm shadow-slate-900/5"
                >
                  <div className="relative h-60 overflow-hidden">
                    <Image
                      src={study.image}
                      alt={study.title}
                      fill
                      className="object-cover transition duration-500 group-hover:scale-105"
                      sizes="(min-width: 1024px) 560px, 100vw"
                    />
                    <div className="absolute inset-x-0 bottom-0 flex items-center justify-between px-6 py-3 text-xs font-semibold uppercase tracking-[0.3em] text-white/80">
                      <span>{study.useCase}</span>
                      <span>{study.timeline}</span>
                    </div>
                    <div className="absolute inset-0 bg-gradient-to-t from-slate-900/80 via-transparent to-transparent opacity-0 transition group-hover:opacity-100" aria-hidden />
                  </div>
                  <div className="space-y-4 px-6 py-6">
                    <div className="flex flex-wrap items-center gap-2 text-xs font-semibold uppercase tracking-[0.3em] text-slate-400">
                      {study.stack.map((tag) => (
                        <span key={tag}>{tag}</span>
                      ))}
                    </div>
                    <h2 className="text-2xl font-semibold text-slate-900">{study.title}</h2>
                    <p className="text-sm text-slate-600">{study.summary}</p>
                    <ul className="space-y-2 text-sm text-slate-600">
                      {study.highlights.map((highlight) => (
                        <li key={highlight} className="flex items-start gap-2">
                          <span className="mt-2 inline-flex h-1.5 w-1.5 rounded-full bg-[#2f81f7]" aria-hidden />
                          <span>{highlight}</span>
                        </li>
                      ))}
                    </ul>
                    <Button asChild variant="ghost" className="mt-2 justify-start text-slate-700 hover:bg-slate-100">
                      <Link href="/start">Discuss a similar build</Link>
                    </Button>
                  </div>
                </motion.article>
              ))}
            </div>
          </div>
        </section>

        <section className="bg-slate-50 py-16">
          <div className="mx-auto max-w-[1200px] px-4 md:px-6">
            <div className="rounded-3xl border border-slate-200 bg-white p-10 shadow-lg shadow-slate-900/10">
              <h2 className="text-2xl font-semibold text-slate-900">Want your product in the showcase?</h2>
              <p className="mt-3 max-w-3xl text-sm text-slate-600">
                We onboard a small number of new builds each month. Selected projects receive the full Axis delivery experience plus a featured case study in this library.
              </p>
              <div className="mt-5 flex flex-col gap-3 sm:flex-row">
                <Button asChild className="bg-[var(--primary)] text-white hover:bg-[#266ee6]">
                  <Link href="/start?offer=portfolio-rate">Apply for a slot</Link>
                </Button>
                <Button asChild variant="ghost" className="text-slate-700 hover:bg-slate-100">
                  <Link href="/call">Talk to the team</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
}
