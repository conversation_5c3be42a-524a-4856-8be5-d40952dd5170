import { ImageResponse } from "next/og";

export const size = { width: 512, height: 512 };
export const contentType = "image/png";

export default function Icon() {
  const stroke = "#808080";
  const strokeWidth = 24;
  const outerRadius = 240;
  const center = 256;
  const centerDot = 40;

  return new ImageResponse(
    (
      <svg
        width="512"
        height="512"
        viewBox="0 0 512 512"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        role="img"
        aria-label="Axis glyph"
      >
        <circle cx={center} cy={center} r={outerRadius} stroke={stroke} strokeWidth={strokeWidth} fill="none" />
        <line x1={center} y1={32} x2={center} y2={480} stroke={stroke} strokeWidth={strokeWidth} />
        <line x1={32} y1={center} x2={480} y2={center} stroke={stroke} strokeWidth={strokeWidth} />
        <circle cx={center} cy={center} r={centerDot} fill={stroke} />
      </svg>
    ),
    { width: size.width, height: size.height }
  );
}
