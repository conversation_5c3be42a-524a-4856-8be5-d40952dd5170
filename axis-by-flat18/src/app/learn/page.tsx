import Link from "next/link";

import { Header } from "@/components/header";
import { Footer } from "@/components/footer";
import { But<PERSON> } from "@/components/ui/button";

const playbooks = [
  {
    title: "Scoping workshop in 45 minutes",
    description: "Run our rapid discovery session to align stakeholders before kickoff.",
    href: "/start?tab=why",
  },
  {
    title: "Investor update template",
    description: "Share weekly progress with angels and advisors using our template.",
    href: "https://flat18.co.uk",
    external: true,
  },
  {
    title: "Launch readiness checklist",
    description: "The list we run before every deploy—use it for your launches too.",
    href: "/support",
  },
];

const stack = [
  { category: "Frontend", items: ["Next.js", "TypeScript", "Tailwind", "Framer Motion"] },
  { category: "Backend", items: ["Supabase", "Hasura", "Postgres", "Serverless functions"] },
  { category: "DevOps", items: ["Vercel", "Railway", "GitHub Actions", "Sentry"] },
  { category: "Collaboration", items: ["Linear", "Notion", "Figma", "Slack"] },
];

const faqItems = [
  {
    question: "How do you communicate during the build?",
    answer: "Async first—weekly demos, daily notes in Linear or Notion, and a shared Slack channel when needed.",
  },
  {
    question: "Can we bring our own stack?",
    answer: "Yes if it’s proven and we can deliver confidently. Otherwise we’ll recommend pragmatic alternatives.",
  },
  {
    question: "What about testing?",
    answer: "We cover accessibility, performance, and functional checks. Automated testing depends on tier and scope.",
  },
  {
    question: "Do you work with internal teams?",
    answer: "Absolutely. We sync on comms cadence, branching strategy, and docs so handover is painless.",
  },
];

const handoverSteps = [
  "Repository access + documentation",
  "Environment variables + secrets",
  "Runbooks and rollback plan",
  "Analytics dashboards + reporting cadence",
  "Support contact paths",
];

export default function LearnPage() {
  return (
    <div className="min-h-screen bg-white text-slate-900">
      <Header />
      <main id="main" className="bg-white">
        <section className="bg-gradient-to-br from-[#eef5ff] to-white py-20">
          <div className="mx-auto max-w-[1200px] px-4 md:px-6">
            <p className="text-xs font-semibold uppercase tracking-[0.3em] text-slate-400">
              Learn
            </p>
            <div className="mt-6 grid gap-10 lg:grid-cols-[2fr,1fr]">
              <div className="space-y-6">
                <h1 className="font-display text-4xl font-semibold leading-tight sm:text-5xl">
                  FAQ, playbooks, and stack notes for a smooth Axis build.
                </h1>
                <p className="max-w-2xl text-lg text-slate-600">
                  We document how we work so you know what to expect before we start. Use the resources below to prepare your team.
                </p>
                <div className="flex flex-col gap-3 sm:flex-row">
                  <Button asChild className="bg-[var(--primary)] text-white hover:bg-[#266ee6]">
                    <Link href="/start">Start a project</Link>
                  </Button>
                  <Button asChild variant="ghost" className="text-slate-700 hover:bg-slate-100">
                    <Link href="/call">Ask a question</Link>
                  </Button>
                </div>
              </div>
              <div className="space-y-3 rounded-3xl border border-slate-200 bg-white p-6 shadow-lg shadow-slate-900/10">
                <p className="text-sm font-semibold uppercase tracking-[0.3em] text-slate-400">
                  Resources inside Axis builds
                </p>
                <ul className="space-y-3 text-sm text-slate-600">
                  <li>Discovery template and decision log</li>
                  <li>Design system tokens + component checklist</li>
                  <li>QA scripts + launch readiness workbook</li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        <section className="py-16">
          <div className="mx-auto max-w-[1200px] px-4 md:px-6">
            <div className="mb-10 text-center">
              <h2 className="font-display text-3xl font-semibold text-slate-900 sm:text-4xl">
                Playbooks to get started fast
              </h2>
            </div>
            <div className="grid gap-6 md:grid-cols-3">
              {playbooks.map((playbook) => (
                <div key={playbook.title} className="flex flex-col gap-4 rounded-3xl border border-slate-200 bg-white p-6 shadow-sm shadow-slate-900/5">
                  <h3 className="text-lg font-semibold text-slate-900">{playbook.title}</h3>
                  <p className="text-sm text-slate-600">{playbook.description}</p>
                  <Link
                    href={playbook.href}
                    className="text-sm font-semibold text-[var(--primary)] hover:text-[#1b66f6]"
                    target={playbook.external ? "_blank" : undefined}
                    rel={playbook.external ? "noopener noreferrer" : undefined}
                  >
                    Open playbook →
                  </Link>
                </div>
              ))}
            </div>
          </div>
        </section>

        <section className="bg-slate-50 py-16">
          <div className="mx-auto max-w-[1200px] px-4 md:px-6">
            <div className="mb-10 text-center">
              <h2 className="font-display text-3xl font-semibold text-slate-900 sm:text-4xl">
                The stack we lean on
              </h2>
              <p className="mt-3 text-lg text-slate-600">
                Proven tools with strong ecosystems. We adapt when your context needs it, but this is our default.
              </p>
            </div>
            <div className="grid gap-6 md:grid-cols-2">
              {stack.map((group) => (
                <div key={group.category} className="rounded-3xl border border-slate-200 bg-white p-6 shadow-sm shadow-slate-900/5">
                  <h3 className="text-lg font-semibold text-slate-900">{group.category}</h3>
                  <div className="mt-3 flex flex-wrap gap-2">
                    {group.items.map((item) => (
                      <span key={item} className="rounded-full bg-slate-100 px-3 py-1 text-xs font-semibold text-slate-600">
                        {item}
                      </span>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        <section className="py-16" id="faq">
          <div className="mx-auto max-w-[1200px] px-4 md:px-6">
            <div className="mb-10 text-center">
              <h2 className="font-display text-3xl font-semibold text-slate-900 sm:text-4xl">
                FAQ
              </h2>
            </div>
            <div className="space-y-4">
              {faqItems.map((faq) => (
                <details key={faq.question} className="rounded-3xl border border-slate-200 bg-white p-6 shadow-sm shadow-slate-900/5">
                  <summary className="cursor-pointer text-lg font-semibold text-slate-900">
                    {faq.question}
                  </summary>
                  <p className="mt-3 text-sm text-slate-600">{faq.answer}</p>
                </details>
              ))}
            </div>
          </div>
        </section>

        <section className="bg-slate-50 py-16">
          <div className="mx-auto max-w-[1200px] px-4 md:px-6">
            <div className="grid gap-8 rounded-3xl border border-slate-200 bg-white p-8 shadow-lg shadow-slate-900/10 lg:grid-cols-[2fr,1fr]">
              <div>
                <h2 className="text-2xl font-semibold text-slate-900">Handover checklist</h2>
                <p className="mt-3 text-sm text-slate-600">
                  Every project ends with a documented handover so you can keep shipping. Use this checklist even if you’re working with another partner.
                </p>
              </div>
              <ul className="space-y-3 text-sm text-slate-600">
                {handoverSteps.map((step) => (
                  <li key={step} className="flex items-start gap-2">
                    <span className="mt-2 inline-flex h-1.5 w-1.5 rounded-full bg-[#35e7cc]" aria-hidden />
                    <span>{step}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
}
