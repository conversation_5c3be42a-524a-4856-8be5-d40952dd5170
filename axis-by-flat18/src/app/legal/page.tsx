import type { <PERSON><PERSON><PERSON> } from "next";
import { Header } from "@/components/header";
import { Footer } from "@/components/footer";
import { LegalContent } from "@/components/legal/legal-content";

export const metadata: Metadata = {
  title: "Legal | Axis by Flat 18 — Terms, Privacy & Policies",
  description: "Legal documentation for Axis by Flat 18 including terms of service, privacy policy, cookie policy, accessibility statement, and acceptable use policy.",
  openGraph: {
    title: "Legal | Axis by Flat 18 — Terms, Privacy & Policies",
    description: "Legal documentation for Axis by Flat 18 including terms of service, privacy policy, cookie policy, accessibility statement, and acceptable use policy.",
    type: "website",
    images: [
      {
        url: "/brand/axis-og-2-1200x630.webp",
        width: 1200,
        height: 630,
        alt: "Axis by Flat 18 hero preview",
      },
      {
        url: "/brand/axis-og-2-600x315.webp",
        width: 600,
        height: 315,
        alt: "Axis by Flat 18 hero preview",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Legal | Axis by Flat 18 — Terms, Privacy & Policies",
    description: "Legal documentation for Axis by Flat 18 including terms of service, privacy policy, cookie policy, accessibility statement, and acceptable use policy.",
    images: ["/brand/axis-og-2-1200x630.webp"],
  },
  robots: {
    index: true,
    follow: true,
  }
};

export default function LegalPage() {
  return (
    <div className="min-h-screen">
      <Header />
      <main>
        <LegalContent />
      </main>
      <Footer />
    </div>
  );
}
