import { NextRequest, NextResponse } from "next/server";
import { getSql, withRetry } from "@/lib/db";

interface LeadData {
  name: string;
  email: string;
  company?: string;
  idea: string;
  tier: "start" | "build" | "pro";
  timeline: string;
  budget: string;
  addon?: string;
}

export async function POST(request: NextRequest) {
  try {
    const data: LeadData = await request.json();

    // Validate required fields
    if (!data.name || !data.email || !data.idea || !data.tier || !data.timeline || !data.budget) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Insert into Neon (Postgres)
    const sql = getSql();
    try {
      await withRetry(() => sql`
        insert into leads (name, email, company, idea, tier, timeline, budget, addon, source, created_at)
        values (${data.name}, ${data.email}, ${data.company || null}, ${data.idea}, ${data.tier}, ${data.timeline}, ${data.budget}, ${data.addon || null}, 'website', now())
      `);
      return NextResponse.json({ success: true });
    } catch (e) {
      console.error("Lead insert error:", e);
      return NextResponse.json({ error: "Failed to save lead" }, { status: 500 });
    }

  } catch (error) {
    console.error("API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
