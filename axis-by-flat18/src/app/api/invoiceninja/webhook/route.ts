import { NextRequest, NextResponse } from "next/server";
import { getSql, withRetry } from "@/lib/db";
import { createChatwootConversation } from "@/lib/chatwoot-server";

type INContact = { email?: string; name?: string };
type INClient = { id?: string; name?: string; contacts?: INContact[] };
type INInvoice = { id?: string; number?: string; invoice_number?: string; total?: number; amount?: number; status?: string; state?: string; invitations?: { link?: string; key?: string }[]; client?: INClient };
type INPayload = {
  event?: string;
  type?: string;
  activity?: { activity_type?: string };
  data?: { event?: string; invoice?: INInvoice; client?: INClient; payment?: unknown };
  invoice?: INInvoice;
  entity?: INInvoice;
  client?: INClient;
  contact?: INContact;
};

function getEventType(payload: INPayload | null | undefined): string {
  if (!payload) return "unknown";
  return (
    payload.event ||
    payload.type ||
    payload.activity?.activity_type ||
    payload?.data?.event ||
    "unknown"
  );
}

function getInvoice(payload: INPayload | null | undefined): INInvoice | null {
  return payload?.invoice || payload?.data?.invoice || payload?.entity || null;
}

function getClientFromPayload(payload: INPayload | null | undefined): { id?: string; name?: string; email?: string } | null {
  // Prefer actual client objects; fall back to contact separately
  const client: INClient | null = payload?.client || payload?.data?.client || payload?.invoice?.client || null;
  const contact: INContact | null = client?.contacts?.[0] || payload?.contact || null;
  if (!client && !contact) return null;
  return {
    id: client?.id,
    name: client?.name || contact?.name,
    email: contact?.email,
  };
}

export async function POST(req: NextRequest) {
  try {
    const secret = process.env.IN_WEBHOOK_SECRET;
    if (secret) {
      const hdr = req.headers.get("x-in-webhook-secret") || req.headers.get("x-webhook-secret");
      if (hdr !== secret) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
      }
    }

    const payload = await req.json();
    const eventType = getEventType(payload);
    const sql = getSql();

    // Always record raw event if table exists
    try {
      await withRetry(() => sql`insert into in_events (event_type, data) values (${eventType}, ${JSON.stringify(payload)}::jsonb)`);
    } catch {
      // Table may not exist; ignore
    }

    // Attempt to sync client record
    const client = getClientFromPayload(payload);
    if (client && client.email) {
      try {
        await withRetry(() => sql`
          insert into clients (external_id, name, email, source)
          values (${client.id || null}, ${client.name || client.email}, ${client.email}, 'invoiceninja')
          on conflict (email) do update set
            name = excluded.name,
            external_id = coalesce(excluded.external_id, clients.external_id),
            source = excluded.source
        `);
      } catch {}
    }

    // Attempt to sync invoice/payment
    const invoice = getInvoice(payload);
    if (invoice) {
      try {
        const reference = invoice.number || invoice?.invoice_number || null;
        const amount = (invoice.total as number | undefined) ?? (invoice.amount as number | undefined) ?? null;
        const status = (invoice.status as string | undefined) ?? (invoice.state as string | undefined) ?? null;
        const rawJson = JSON.stringify(invoice);
        await withRetry(() => sql`
          insert into payments (external_id, reference, amount, status, provider, raw, email)
          values (${invoice.id || null}, ${reference}, ${amount}, ${status}, 'invoiceninja', ${rawJson}::jsonb, ${client?.email || null})
          on conflict (external_id) do update set
            reference = excluded.reference,
            amount = excluded.amount,
            status = excluded.status,
            provider = excluded.provider,
            raw = excluded.raw,
            email = excluded.email
        `);
        // Link to most recent lead by email, if available
        if (client?.email) {
          const leads = (await withRetry(() => sql`select id from leads where email = ${client.email} order by created_at desc limit 1`)) as { id: number }[];
          const leadId = leads?.[0]?.id;
          if (leadId) {
            try {
              await withRetry(() => sql`update payments set lead_id = ${leadId} where external_id = ${invoice.id}`);
            } catch {}
          }
        }
      } catch {}
    }

    // Optional Slack notification
    try {
      const slackUrl = process.env.SLACK_WEBHOOK_URL;
      if (slackUrl) {
        const c = getClientFromPayload(payload);
        const host = process.env.IN_HOST_URL?.replace(/\/$/, "") || "";
        const ref = invoice?.number || invoice?.invoice_number || invoice?.id || 'n/a';
        const amount = invoice?.total || invoice?.amount || 'n/a';
        const status = invoice?.status || 'n/a';
        const adminLink = invoice?.id ? `${host}/invoices/${invoice.id}` : host;
        const text = `🧾 Invoice Ninja: ${eventType}`;
        const blocks = [
          { type: "header", text: { type: "plain_text", text: `Invoice Ninja: ${eventType}` } },
          { type: "section", fields: [
            { type: "mrkdwn", text: `*Client*\n${c?.email || c?.name || 'unknown'}` },
            { type: "mrkdwn", text: `*Reference*\n${ref}` },
            { type: "mrkdwn", text: `*Amount*\n${amount}` },
            { type: "mrkdwn", text: `*Status*\n${status}` },
          ]},
          { type: "actions", elements: [
            { type: "button", text: { type: "plain_text", text: "Open in Admin" }, url: adminLink }
          ]}
        ];
        await fetch(slackUrl, {
// Chatwoot handled in shared lib
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ text, blocks }),
        });
      }
    } catch {
      // ignore notification errors
    }

    // If payment paid, notify channels
    const isPaid = (invoice?.status || '').toLowerCase() === 'paid' || Boolean(payload?.payment || payload?.data?.payment);
    if (isPaid) {
      const summary = `Payment received from ${client?.email || client?.name || 'unknown'} for ${invoice?.number || invoice?.invoice_number || invoice?.id || 'n/a'} (amount: ${invoice?.total || invoice?.amount || 'n/a'})`;
      await createChatwootConversation(client?.email, summary);
    }

    return NextResponse.json({ ok: true });
  } catch (e) {
    console.error("IN webhook error", e);
    return NextResponse.json({ error: "internal error" }, { status: 500 });
  }
}
