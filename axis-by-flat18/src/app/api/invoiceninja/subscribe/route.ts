import { NextRequest, NextResponse } from "next/server";
import { findOrCreateClient, getPublicSubscriptionUrl, createInvoiceInvitationForTier, type Tier } from "@/lib/invoiceninja";

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const name = (body?.name as string | undefined)?.trim();
    const email = (body?.email as string | undefined)?.trim();
    const tier = body?.tier as Tier | undefined;
    const mode = (body?.mode as string | undefined) || "subscription"; // 'subscription' | 'invoice'
    const utm = body?.utm as Record<string, unknown> | undefined;

    if (!email || !tier || !["start", "build", "pro"].includes(tier)) {
      return NextResponse.json({ error: "Missing required fields (email, tier)" }, { status: 400 });
    }

    // Prefer public subscription link if available and mode is subscription
    const host = process.env.IN_HOST_URL || "";
    const apiToken = process.env.IN_API_TOKEN || "";

    if (mode === "subscription") {
      const subUrl = getPublicSubscriptionUrl(tier);
      if (subUrl) {
        try {
          const u = new URL(subUrl);
          if (email) u.searchParams.set("email", email);
          return NextResponse.json({ redirectUrl: u.toString() });
        } catch {
          // If URL constructor fails, fall back to string concatenation
          const glue = subUrl.includes("?") ? "&" : "?";
          const withEmail = email ? `${subUrl}${glue}email=${encodeURIComponent(email)}` : subUrl;
          return NextResponse.json({ redirectUrl: withEmail });
        }
      }
      // If no subscription url, fall back to invoice mode automatically
    }

    // Invoice (deposit or full) flow via API
    if (!host || !apiToken) {
      return NextResponse.json({ error: "Invoice Ninja API not configured" }, { status: 500 });
    }

    const client = await findOrCreateClient(host, apiToken, { name, email, utm });
    const clientId = client?.id || client?.data?.id;
    if (!clientId) {
      return NextResponse.json({ error: "Unable to resolve client id" }, { status: 500 });
    }
    const link = await createInvoiceInvitationForTier(host, apiToken, { clientId, tier, utm });
    return NextResponse.json({ redirectUrl: link });
  } catch (e: unknown) {
    console.error("Invoice Ninja subscribe error", e);
    return NextResponse.json({ error: "Internal error" }, { status: 500 });
  }
}
