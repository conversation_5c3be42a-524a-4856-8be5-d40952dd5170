import { NextRequest, NextResponse } from "next/server";
import { findOrCreateClient, createQuoteInvitationForClient } from "@/lib/invoiceninja";
import { createChatwootConversation } from "@/lib/chatwoot-server";

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const email = (body?.email as string | undefined)?.trim();
    const name = (body?.name as string | undefined)?.trim();
    if (!email) return NextResponse.json({ error: "Missing email" }, { status: 400 });

    const host = process.env.IN_HOST_URL || "";
    const apiToken = process.env.IN_API_TOKEN || "";
    if (!host || !apiToken) return NextResponse.json({ error: "IN not configured" }, { status: 500 });

    const client = await findOrCreateClient(host, apiToken, { email, name });
    const clientId = client?.id || client?.data?.id;
    if (!clientId) return NextResponse.json({ error: "Client not resolved" }, { status: 500 });
    const link = await createQuoteInvitationForClient(host, apiToken, { clientId, notes: "Portal login" });
    // Post a note to Chatwoot for continuity
    await createChatwootConversation(email, `Magic login requested. Link: ${link}`);
    return NextResponse.json({ redirectUrl: link });
  } catch (e) {
    console.error("IN portal-invite error", e);
    return NextResponse.json({ error: "Internal error" }, { status: 500 });
  }
}
