import Link from "next/link";
import Image from "next/image";

import { Head<PERSON> } from "@/components/header";
import { Footer } from "@/components/footer";
import { Button } from "@/components/ui/button";
import { HowWeWork } from "@/components/home/<USER>";

const artefacts = [
  {
    title: "Kickoff",
    description: "We frame the problem, success signals, and measurement plan in a shared brief within hours.",
    image: "/placeholders/img/mockup-2.png",
    bullets: [
      "Scope brief + success metrics",
      "User + stakeholder map",
      "Risks and dependencies log",
    ],
  },
  {
    title: "Design",
    description: "Clickable wireframes, tokens, and copy decks to approve flows before build starts.",
    image: "/placeholders/img/mockup-3.png",
    bullets: [
      "Wireframes + prototype",
      "UI system tokens",
      "Copy and state variations",
    ],
  },
  {
    title: "Build",
    description: "Production Next.js and TypeScript code reviewed daily, with QA baked into each sprint.",
    image: "/placeholders/img/mockup-1.png",
    bullets: [
      "Component library & stories",
      "Testing and monitoring hooks",
      "Analytics + event plan",
    ],
  },
  {
    title: "Launch",
    description: "Runbook, launch checklist, and ownership transfer so your team is confident on day one.",
    image: "/brand/axis-lockup-grey.svg",
    bullets: [
      "Launch plan + rollback",
      "Credentials + env handover",
      "30-day stabilisation window",
    ],
  },
];

const commitments = [
  {
    title: "Weekly demos",
    copy: "Every Friday you get a recorded walkthrough covering progress, blockers, and next steps."
  },
  {
    title: "Transparent backlog",
    copy: "You see every task, status, and decision inside our shared Notion or Linear workspace."
  },
  {
    title: "QA guardrails",
    copy: "Accessibility, performance, and browser sweeps are pre-booked for each tier."
  },
  {
    title: "Launch partner",
    copy: "We deploy with you, document everything, and monitor in the stabilisation window."
  },
];

const sla = [
  "Response within 1 business day during build",
  "Incident triage within 4 hours during stabilisation",
  "Actionable launch checklist 5 days before go-live",
  "Post-launch retrospective + recommendations",
];

export default function ProcessPage() {
  return (
    <div className="min-h-screen bg-white text-slate-900">
      <Header />
      <main id="main" className="bg-white">
        <section className="bg-gradient-to-br from-[#eef5ff] to-white py-20">
          <div className="mx-auto max-w-[1200px] px-4 md:px-6">
            <p className="text-xs font-semibold uppercase tracking-[0.3em] text-slate-400">
              Process
            </p>
            <div className="mt-6 grid gap-10 lg:grid-cols-[2fr,1fr]">
              <div className="space-y-6">
                <h1 className="font-display text-4xl font-semibold leading-tight sm:text-5xl">
                  Structured in weeks, so you can launch with confidence.
                </h1>
                <p className="max-w-2xl text-lg text-slate-600">
                  Axis follows a four-step operating system: scope, design, build, launch. You see the artefacts at every phase, the handoff is rehearsed, and nothing slips through the cracks.
                </p>
                <div className="flex flex-col gap-3 sm:flex-row">
                  <Button asChild size="lg" className="bg-[var(--primary)] text-white hover:bg-[#266ee6]">
                    <Link href="/start">Start a project</Link>
                  </Button>
                  <Button asChild variant="ghost" size="lg" className="text-slate-700 hover:bg-slate-100">
                    <Link href="/call">Book a call</Link>
                  </Button>
                </div>
              </div>
              <div className="space-y-4 rounded-3xl border border-slate-200 bg-white p-6 shadow-lg shadow-slate-900/10">
                <h2 className="text-lg font-semibold">Build rhythm</h2>
                <ul className="space-y-3 text-sm text-slate-600">
                  <li>Mon: goals + backlog review</li>
                  <li>Wed: async loom check-in</li>
                  <li>Fri: live demo & decisions</li>
                  <li>Sun: written status + metrics</li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        <HowWeWork />

        <section className="bg-white py-16">
          <div className="mx-auto max-w-[1200px] px-4 md:px-6">
            <div className="mb-10 text-center">
              <h2 className="font-display text-3xl font-semibold text-slate-900 sm:text-4xl">
                Artefacts you can rely on
              </h2>
              <p className="mt-3 text-lg text-slate-600">
                We share concrete outputs each step so stakeholders, investors, and internal teams stay aligned.
              </p>
            </div>
            <div className="grid gap-8 md:grid-cols-2">
              {artefacts.map((item) => (
                <div key={item.title} className="flex flex-col gap-4 rounded-3xl border border-slate-200 bg-white p-6 shadow-sm shadow-slate-900/5">
                  <div className="relative h-48 overflow-hidden rounded-2xl bg-slate-100">
                    <Image
                      src={item.image}
                      alt={`${item.title} artefact`}
                      fill
                      className="object-cover"
                      sizes="(min-width: 768px) 50vw, 100vw"
                    />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-slate-900">{item.title}</h3>
                    <p className="mt-2 text-sm text-slate-600">{item.description}</p>
                  </div>
                  <ul className="space-y-2 text-sm text-slate-600">
                    {item.bullets.map((bullet) => (
                      <li key={bullet} className="flex items-start gap-2">
                        <span className="mt-2 inline-flex h-1.5 w-1.5 rounded-full bg-[#2f81f7]" aria-hidden />
                        <span>{bullet}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </section>

        <section className="bg-slate-50 py-16">
          <div className="mx-auto max-w-[1200px] px-4 md:px-6">
            <div className="mb-10 text-center">
              <h2 className="font-display text-3xl font-semibold text-slate-900 sm:text-4xl">
                What we commit to
              </h2>
              <p className="mt-3 text-lg text-slate-600">
                Delivery discipline means predictable outcomes. These are the non-negotiables baked into every build.
              </p>
            </div>
            <div className="grid gap-6 md:grid-cols-2">
              {commitments.map((item) => (
                <div key={item.title} className="rounded-3xl border border-slate-200 bg-white p-6 shadow-sm shadow-slate-900/5">
                  <h3 className="text-lg font-semibold text-slate-900">{item.title}</h3>
                  <p className="mt-2 text-sm text-slate-600">{item.copy}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        <section className="bg-white py-16">
          <div className="mx-auto max-w-[1200px] px-4 md:px-6">
            <div className="grid gap-8 rounded-3xl border border-slate-200 bg-white p-8 shadow-lg shadow-slate-900/10 lg:grid-cols-[2fr,1fr]">
              <div>
                <h2 className="text-2xl font-semibold text-slate-900">Service level at a glance</h2>
                <p className="mt-3 text-sm text-slate-600">
                  We operate on business-day schedules with emergency paths during stabilisation. Complex SLAs roll into the Flat 18 retainer when needed.
                </p>
              </div>
              <ul className="space-y-3 text-sm text-slate-600">
                {sla.map((line) => (
                  <li key={line} className="flex items-start gap-2">
                    <span className="mt-2 inline-flex h-1.5 w-1.5 rounded-full bg-[#35e7cc]" aria-hidden />
                    <span>{line}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
}
