"use client";
import { <PERSON><PERSON> } from "@/components/header";
import { Footer } from "@/components/footer";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { openChat } from "@/components/chatwoot";
import { CreditCard, MessageCircle, FileText } from "lucide-react";
import { useEffect, useState } from "react";
import Link from "next/link";
import { trackOnboardingStep, trackCtaClick } from "@/lib/twitter";

export default function OnboardPage() {
  const [email, setEmail] = useState("");
  const [name, setName] = useState("");
  const [tier, setTier] = useState<"start" | "build" | "pro">("start");
  const [loading, setLoading] = useState<false | "subscription" | "invoice">(false);
  const [utm, setUtm] = useState<Record<string, unknown> | undefined>(undefined);
  const [magic, setMagic] = useState<{ url: string } | null>(null);
  const [sub, setSub] = useState<{ url: string } | null>(null);

  useEffect(() => {
    try {
      const params = new URLSearchParams(window.location.search);
      const utmObj: Record<string, string> = {};
      ["utm_source", "utm_medium", "utm_campaign", "utm_content", "utm_term"].forEach((k) => {
        const v = params.get(k) || (sessionStorage.getItem(k) ?? undefined);
        if (v) utmObj[k] = v;
      });
      // Store for later pages
      Object.entries(utmObj).forEach(([k, v]) => sessionStorage.setItem(k, v as string));
      setUtm(Object.keys(utmObj).length ? utmObj : undefined);
    } catch {
      // ignore
    }
    // track onboarding page view
    try { trackOnboardingStep({ step: "view" }); } catch {}
  }, []);

  const subscribe = async (mode: "subscription" | "invoice") => {
    if (!email) {
      alert("Please enter your email first.");
      return;
    }
    setLoading(mode);
    try {
      const res = await fetch("/api/invoiceninja/subscribe", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ name, email, tier, mode, utm }),
      });
      const data = await res.json();
      if (res.ok && data?.redirectUrl) {
        setSub({ url: data.redirectUrl as string });
        setTimeout(() => { try { window.location.href = data.redirectUrl as string; } catch {} }, 600);
      } else {
        alert(data?.error || "Unable to start subscription.");
      }
    } catch {
      alert("Network error. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen">
      <Header />
      <main className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h1 className="font-display text-5xl font-bold text-axis-text mb-4">
              Onboarding
            </h1>
            <p className="text-xl text-[var(--text-300)] max-w-2xl mx-auto">
              Pick the path that suits you best — we support quick project submissions, subscription self-onboarding, or live chat to get started.
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-3 max-w-6xl mx-auto">
            {/* Submit Project */}
            <Card className="border-border hover:border-axis-accent-1/50 transition-all duration-300">
              <CardHeader>
                <div className="w-10 h-10 rounded-lg bg-axis-accent-1/10 text-axis-accent-1 flex items-center justify-center mb-3">
                  <FileText className="w-5 h-5" />
                </div>
                <CardTitle className="font-display text-2xl text-axis-text">
                  Submit Your Project
                </CardTitle>
                <CardDescription className="text-[var(--text-300)]">
                  Use our brief form to submit your project for consideration.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm text-[var(--text-300)] mb-6">
                  <p>Reviewed within 24 hours</p>
                  <p>Scoping call and proposal</p>
                </div>
                <Button asChild size="lg" className="w-full">
                  <Link href="/start" onClick={() => { trackOnboardingStep({ step: "submit-project" }); trackCtaClick({ label: "Submit Project", location: "onboarding", href: "/start" }); }}>Submit Project</Link>
                </Button>
              </CardContent>
            </Card>

            {/* Subscribe & Self-Onboard */}
            <Card className="border-2 border-axis-accent-2 shadow-lg shadow-axis-accent-2/10">
              <CardHeader>
                <div className="w-10 h-10 rounded-lg bg-axis-accent-2/10 text-axis-accent-2 flex items-center justify-center mb-3">
                  <CreditCard className="w-5 h-5" />
                </div>
                <CardTitle className="font-display text-2xl text-axis-text">
                  Subscribe & Self‑Onboard
                </CardTitle>
                <CardDescription className="text-[var(--text-300)]">
                  Purchase a subscription and be auto‑provisioned via Invoice Ninja.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm text-[var(--text-300)] mb-6">
                  <p>Instant access to your client portal</p>
                  <p>Invoices, subscriptions, and onboarding in one place</p>
                </div>
                <div className="grid gap-3 mb-4">
                  <Input placeholder="Your email" type="email" value={email} onChange={(e) => setEmail(e.target.value)} />
                  <Input placeholder="Your name (optional)" value={name} onChange={(e) => setName(e.target.value)} />
                  <Select value={tier} onValueChange={(v) => setTier(v as 'start'|'build'|'pro')}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select tier" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="start">Axis Start</SelectItem>
                      <SelectItem value="build">Axis Build</SelectItem>
                      <SelectItem value="pro">Axis Pro</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid gap-2">
                  <Button size="lg" className="w-full bg-axis-accent-2 hover:bg-axis-accent-2/90" onClick={() => { trackOnboardingStep({ step: "subscribe-click", tier }); subscribe("subscription"); }} disabled={!!loading}>
                    {loading === "subscription" ? "Redirecting…" : "Subscribe Now"}
                  </Button>
                  <Button size="sm" variant="outline" className="w-full" onClick={() => { trackOnboardingStep({ step: "invoice-click", tier }); subscribe("invoice"); }} disabled={!!loading}>
                    {loading === "invoice" ? "Preparing invoice…" : "Pay Deposit / Invoice"}
                  </Button>
                </div>
                {sub && (
                  <div className="mt-4 p-3 rounded-md border border-axis-accent-2/30 bg-axis-accent-2/10">
                    <p className="text-sm text-axis-text">Redirecting you to secure checkout…</p>
                    <div className="mt-2 flex gap-2">
                      <Button size="sm" onClick={()=> window.location.href = sub.url}>Open Checkout</Button>
                      <Button size="sm" variant="outline" onClick={async ()=>{ await navigator.clipboard.writeText(sub.url); alert('Link copied'); }}>Copy Link</Button>
                    </div>
                    <p className="text-xs text-[var(--text-500)] mt-2">If it doesn&apos;t open automatically, use the buttons above.</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Live Chat */}
            <Card className="border-border hover:border-axis-accent-3/50 transition-all duration-300">
              <CardHeader>
                <div className="w-10 h-10 rounded-lg bg-axis-accent-3/10 text-axis-accent-3 flex items-center justify-center mb-3">
                  <MessageCircle className="w-5 h-5" />
                </div>
                <CardTitle className="font-display text-2xl text-axis-text">
                  Live Chat with Us
                </CardTitle>
                <CardDescription className="text-[var(--text-300)]">
                  Prefer to talk first? Open a chat and we&apos;ll guide you.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm text-[var(--text-300)] mb-6">
                  <p>Fastest way to get started</p>
                  <p>We&apos;ll point you to the right path</p>
                </div>
                <Button size="lg" className="w-full" onClick={() => { trackOnboardingStep({ step: "live-chat-click" }); openChat(); }}>
                  Start Live Chat
                </Button>
                <div className="mt-4 grid gap-2">
                  <div className="text-xs text-[var(--text-500)]">Existing customer?</div>
                  <div className="grid gap-2 sm:grid-cols-3">
                    <Input placeholder="Your email" type="email" value={email} onChange={(e)=>setEmail(e.target.value)} className="sm:col-span-2" />
                    <Button variant="outline" onClick={async ()=>{
                      if (!email) { alert('Enter your email'); return; }
                      try { trackOnboardingStep({ step: "magic-login", email }); } catch {}
                      setLoading('subscription');
                      try {
                        const res = await fetch('/api/invoiceninja/portal-invite', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ email, name }) });
                        const data = await res.json();
                        if (res.ok && data?.redirectUrl) {
                          setMagic({ url: data.redirectUrl as string });
                        } else {
                          alert(data?.error || 'Unable to create login link');
                        }
                      } finally {
                        setLoading(false);
                      }
                    }}>
                      Magic Login
                    </Button>
                  </div>
                  <p className="text-xs text-[var(--text-500)]">We’ll open a secure portal link to authenticate you automatically.</p>
                  {magic && (
                    <div className="mt-3 p-3 rounded-md border border-axis-accent-1/30 bg-axis-accent-1/10">
                      <p className="text-sm text-axis-text">
                        Magic login link generated for <span className="font-semibold">{email}</span>.
                      </p>
                      <div className="mt-2 flex gap-2">
                        <Button size="sm" onClick={()=> window.location.href = magic.url}>Open Client Portal</Button>
                        <Button size="sm" variant="outline" onClick={async ()=>{ await navigator.clipboard.writeText(magic.url); alert('Link copied'); }}>Copy Link</Button>
                      </div>
                      <p className="text-xs text-[var(--text-500)] mt-2">We also sent a note to our team; feel free to chat if you need help.</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}
