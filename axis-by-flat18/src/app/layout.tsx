import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { UmamiAnalytics, AckeeAnalytics } from "@/components/analytics";
import { OrganizationStructuredData, ProductStructuredData, ServiceStructuredData } from "@/components/structured-data";
import <PERSON>ript from "next/script";
import { ChatwootWidget } from "@/components/chatwoot";
import { UTMInit } from "@/components/utm-init";
import { CommandPalette } from "@/components/command-palette";
import { SpeedInsights } from "@vercel/speed-insights/next"

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "Axis — MVPs in weeks. Fixed scope, fixed price.",
  description: "<PERSON> is a new, senior team from Flat 18 delivering investor-ready MVPs with transparent pricing and rapid timelines.",
  keywords: "MVP development, rapid prototyping, startup development, web apps, mobile apps, DeFi development, crypto apps, Next.js development",
  authors: [{ name: "Flat 18" }],
  creator: "Flat 18",
  publisher: "Axis by Flat 18",
  icons: {
    icon: "/brand/axis-icon-grey.svg",
    shortcut: "/brand/axis-icon-grey.svg",
    apple: "/brand/axis-icon-grey.svg",
  },
  openGraph: {
    title: "Axis — MVPs in weeks. Fixed scope, fixed price.",
    description: "Axis is a new, senior team from Flat 18 delivering investor-ready MVPs with transparent pricing and rapid timelines.",
    url: "https://axis-web.dev",
    siteName: "Axis by Flat 18",
    locale: "en_GB",
    type: "website",
    images: [
      {
        url: "/brand/axis-og-2-1200x630.webp",
        width: 1200,
        height: 630,
        alt: "Axis by Flat 18 hero preview",
      },
      {
        url: "/brand/axis-og-2-600x315.webp",
        width: 600,
        height: 315,
        alt: "Axis by Flat 18 hero preview",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Axis — MVPs in weeks. Fixed scope, fixed price.",
    description: "Axis is a new, senior team from Flat 18 delivering investor-ready MVPs with transparent pricing and rapid timelines.",
    creator: "@flat18co",
    images: ["/brand/axis-og-2-1200x630.webp"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en-GB" data-theme="light" suppressHydrationWarning>
      <body className={`${inter.variable} font-body antialiased`}>
        <a href="#main" className="skip-link">
          Skip to content
        </a>
        {children}
        <CommandPalette />
        <UTMInit />
        {/* Google tag (gtag.js) via env var */}
        {process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID ? (
          <>
            <Script
              src={`https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID}`}
              strategy="afterInteractive"
            />
            <Script id="ga-gtag-init" strategy="afterInteractive">
              {`
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);} 
                gtag('js', new Date());
                gtag('config', '${process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID}');
              `}
            </Script>
          </>
        ) : null}
        {/* Twitter conversion tracking base code */}
        {process.env.NODE_ENV === "production" ? (
          <Script id="twitter-pixel" strategy="afterInteractive">
            {`!function(e,t,n,s,u,a){e.twq||(s=e.twq=function(){s.exe?s.exe.apply(s,arguments):s.queue.push(arguments);
},s.version='1.1',s.queue=[],u=t.createElement(n),u.async=!0,u.src='https://static.ads-twitter.com/uwt.js',
a=t.getElementsByTagName(n)[0],a.parentNode.insertBefore(u,a))}(window,document,'script');
twq('config','qfnk1');`}
          </Script>
        ) : null}
        {/* End Twitter conversion tracking base code */}
        <SpeedInsights/>
        <UmamiAnalytics 
          websiteId={process.env.NEXT_PUBLIC_UMAMI_WEBSITE_ID || ""} 
          src={process.env.NEXT_PUBLIC_UMAMI_SRC}
        />
        <AckeeAnalytics
          server={process.env.NEXT_PUBLIC_ACKEE_SERVER || ""}
          domainId={process.env.NEXT_PUBLIC_ACKEE_DOMAIN_ID || ""}
          src={process.env.NEXT_PUBLIC_ACKEE_SRC || undefined}
          enabled={Boolean(process.env.NEXT_PUBLIC_ACKEE_SERVER && process.env.NEXT_PUBLIC_ACKEE_DOMAIN_ID)}
        />
        <ChatwootWidget 
          websiteToken={process.env.NEXT_PUBLIC_CHATWOOT_WEBSITE_TOKEN}
          baseUrl={process.env.NEXT_PUBLIC_CHATWOOT_BASE_URL}
          enabled={Boolean(process.env.NEXT_PUBLIC_CHATWOOT_WEBSITE_TOKEN && process.env.NEXT_PUBLIC_CHATWOOT_BASE_URL)}
        />
        <OrganizationStructuredData />
        <ProductStructuredData />
        <ServiceStructuredData />
      </body>
    </html>
  );
}
