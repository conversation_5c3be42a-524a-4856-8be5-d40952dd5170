import { Header } from "@/components/header";
import { Footer } from "@/components/footer";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";

const services = [
  {
    id: "start",
    name: "Axis Start",
    summary: "Fast proof-of-concept or landing system to validate in public.",
    price: "£1,495",
    timeline: "~2 weeks",
    includes: [
      "3–5 key screens with responsive states",
      "Foundational auth or data feed",
      "Deployment, analytics, and launch checklist",
    ],
    deliverables: [
      "High-fidelity UI and copy deck",
      "Production Next.js build",
      "Documentation + loom walkthrough",
    ],
    who: "Founders seeking signal, early waitlist activations, single-scope pilots",
    constraints: [
      "Limited integrations (1 primary)",
      "Single primary user journey",
      "Focus on speed to validate",
    ],
  },
  {
    id: "build",
    name: "Axis Build",
    summary: "Flagship MVP covering core journeys, integrations, and production handover.",
    price: "£2,995",
    timeline: "~3–4 weeks",
    includes: [
      "Component-driven UI with design tokens",
      "Auth, database, and 1–3 integrations",
      "Monitoring, analytics, and QA budget",
    ],
    deliverables: [
      "Feature-complete Next.js app",
      "Design files and system primitives",
      "Launch plan + runbooks + post-launch support",
    ],
    who: "Seed-stage teams shipping first version to investors or early customers",
    constraints: [
      "Scope locked to defined journeys",
      "Integrations limited to 3",
      "One primary surface (web) by default",
    ],
  },
  {
    id: "pro",
    name: "Axis Pro",
    summary: "Complex demo or operations build with multiple surfaces and admin workflows.",
    price: "£5,495",
    timeline: "~5–6 weeks",
    includes: [
      "Expanded features, admin area, and roles",
      "3–6 integrations or automations",
      "Performance budgets, accessibility sweeps",
    ],
    deliverables: [
      "Multi-surface experience (web + ops)",
      "Advanced analytics & instrumentation",
      "Detailed handover + stakeholder enablement",
    ],
    who: "Teams preparing for fundraising demos or replacing manual ops tooling",
    constraints: [
      "No speculative R&D or ML",
      "Scope framed around launchable MVP",
      "Longer sprints for QA + compliance",
    ],
  },
  {
    id: "scale",
    name: "Scale Support",
    summary: "Ongoing monthly support for fixes, polish, and light iterations.",
    price: "£750/month",
    timeline: "Rolling",
    includes: [
      "Bug fixes, upkeep, and small enhancements",
      "Monitoring + incident triage",
      "Monthly report + backlog grooming",
    ],
    deliverables: [
      "Prioritised request queue",
      "Release notes and documentation",
      "Fast path back into Flat 18 for larger work",
    ],
    who: "Founders iterating on live product or prepping for a wider Flat 18 engagement",
    constraints: [
      "Suited to incremental scope",
      "Projects built by Axis or reviewed",
      "Pauses require 2 weeks' notice",
    ],
  },
];

export default function ServicesPage() {
  return (
    <div className="min-h-screen bg-white text-slate-900">
      <Header />
      <main id="main" className="bg-white">
        <section className="bg-gradient-to-br from-[#eef5ff] to-white py-20">
          <div className="mx-auto max-w-[1200px] px-4 md:px-6">
            <p className="text-xs font-semibold uppercase tracking-[0.3em] text-slate-400">
              Services
            </p>
            <div className="mt-6 grid gap-10 lg:grid-cols-[2fr,1fr]">
              <div className="space-y-6">
                <h1 className="font-display text-4xl font-semibold leading-tight sm:text-5xl">
                  Fixed-scope Axis packages that ship investor-ready builds.
                </h1>
                <p className="max-w-2xl text-lg text-slate-600">
                  Choose the tier that matches your roadmap. Each package includes the same disciplined delivery practice—scope lock, weekly demos, QA guardrails, and launch-ready handover.
                </p>
                <div className="flex flex-col gap-3 sm:flex-row">
                  <Button asChild size="lg" className="bg-[var(--primary)] text-white hover:bg-[#266ee6]">
                    <Link href="/start">Start a project</Link>
                  </Button>
                  <Button asChild variant="ghost" size="lg" className="text-slate-700 hover:bg-slate-100">
                    <Link href="/pricing">Compare pricing</Link>
                  </Button>
                </div>
              </div>
              <div className="space-y-3 rounded-3xl border border-slate-200 bg-white p-6 shadow-lg shadow-slate-900/10">
                <p className="text-sm font-semibold uppercase tracking-[0.3em] text-slate-400">
                  Every engagement includes
                </p>
                <ul className="space-y-3 text-sm text-slate-600">
                  <li>Project workspace with shared decisions and artefacts</li>
                  <li>QA budget covering accessibility, performance, and cross-browser</li>
                  <li>Structured launch plan, runbooks, and 30-day stabilisation window</li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        <section className="py-16">
          <div className="mx-auto max-w-[1200px] space-y-12 px-4 md:px-6">
            {services.map((service) => (
              <div
                key={service.id}
                id={service.id}
                className="grid gap-8 rounded-3xl border border-slate-200 bg-white p-8 shadow-md shadow-slate-900/5 lg:grid-cols-[2fr,1fr]"
              >
                <div className="space-y-6">
                  <div>
                    <p className="text-xs font-semibold uppercase tracking-[0.3em] text-slate-400">
                      {service.timeline}
                    </p>
                    <h2 className="mt-2 text-2xl font-semibold">{service.name}</h2>
                    <p className="mt-3 text-sm text-slate-600">{service.summary}</p>
                  </div>

                  <div className="grid gap-6 sm:grid-cols-2">
                    <div>
                      <h3 className="text-sm font-semibold uppercase tracking-[0.3em] text-slate-500">
                        What’s included
                      </h3>
                      <ul className="mt-3 space-y-2 text-sm text-slate-600">
                        {service.includes.map((item) => (
                          <li key={item} className="flex items-start gap-2">
                            <span className="mt-2 inline-flex h-1.5 w-1.5 rounded-full bg-[#2f81f7]" aria-hidden />
                            <span>{item}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <h3 className="text-sm font-semibold uppercase tracking-[0.3em] text-slate-500">
                        Deliverables
                      </h3>
                      <ul className="mt-3 space-y-2 text-sm text-slate-600">
                        {service.deliverables.map((item) => (
                          <li key={item} className="flex items-start gap-2">
                            <span className="mt-2 inline-flex h-1.5 w-1.5 rounded-full bg-[#35e7cc]" aria-hidden />
                            <span>{item}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  <div className="grid gap-6 sm:grid-cols-2">
                    <div>
                      <h3 className="text-sm font-semibold uppercase tracking-[0.3em] text-slate-500">
                        Who it’s for
                      </h3>
                      <p className="mt-3 text-sm text-slate-600">{service.who}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-semibold uppercase tracking-[0.3em] text-slate-500">
                        Constraints
                      </h3>
                      <ul className="mt-3 space-y-2 text-sm text-slate-600">
                        {service.constraints.map((constraint) => (
                          <li key={constraint} className="flex items-start gap-2">
                            <span className="mt-2 inline-flex h-1.5 w-1.5 rounded-full bg-slate-300" aria-hidden />
                            <span>{constraint}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>

                <div className="space-y-6 rounded-3xl border border-slate-200 bg-slate-50 p-6 lg:sticky lg:top-28">
                  <div>
                    <span className="text-xs font-semibold uppercase tracking-[0.3em] text-slate-500">
                      Investment
                    </span>
                    <p className="mt-2 text-3xl font-semibold text-slate-900">{service.price}</p>
                    <p className="text-sm text-slate-500">50% deposit • 50% pre-launch</p>
                  </div>
                  <div className="space-y-3 text-sm text-slate-600">
                    <p>Includes 30-day stabilisation window and direct access to the team.</p>
                    {service.id !== "scale" ? (
                      <Button asChild className="w-full bg-[var(--primary)] text-white hover:bg-[#266ee6]">
                        <Link href={`/start?tier=${service.id}`}>Start Axis {service.name.split(" ")[1]}</Link>
                      </Button>
                    ) : (
                      <Button asChild className="w-full bg-[var(--cta)] text-[var(--bg)] hover:bg-[#15d7c0]">
                        <Link href="/support">Discuss support</Link>
                      </Button>
                    )}
                    <Button asChild variant="ghost" className="w-full text-slate-700 hover:bg-slate-100">
                      <Link href="/call">Book a call</Link>
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </section>

        <section className="bg-slate-50 py-16">
          <div className="mx-auto max-w-[1200px] px-4 md:px-6">
            <div className="rounded-3xl border border-slate-200 bg-white p-10 shadow-lg shadow-slate-900/10">
              <h2 className="text-2xl font-semibold text-slate-900">Need a bespoke scope?</h2>
              <p className="mt-3 max-w-3xl text-sm text-slate-600">
                We’ll help you choose the right tier during scoping. If your requirements exceed a package, we document the path to either an Axis Pro extension or a Flat 18 engagement. Clarity before we build.
              </p>
              <div className="mt-5 flex flex-col gap-3 sm:flex-row">
                <Button asChild className="bg-[var(--primary)] text-white hover:bg-[#266ee6]">
                  <Link href="/start">Start a project</Link>
                </Button>
                <Button asChild variant="ghost" className="text-slate-700 hover:bg-slate-100">
                  <Link href="/call">Book a call</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
}
