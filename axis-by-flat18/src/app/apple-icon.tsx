import { ImageResponse } from "next/og";

export const size = { width: 180, height: 180 };
export const contentType = "image/png";

export default function AppleIcon() {
  const stroke = "#808080";
  const targetStroke = 24; // matches SVG in /public/brand icon

  return new ImageResponse(
    (
      <svg
        width="180"
        height="180"
        viewBox="0 0 512 512"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        role="img"
        aria-label="Axis glyph"
      >
        <circle cx="256" cy="256" r="240" stroke={stroke} strokeWidth={targetStroke} fill="none" />
        <line x1="256" y1="32" x2="256" y2="480" stroke={stroke} strokeWidth={targetStroke} />
        <line x1="32" y1="256" x2="480" y2="256" stroke={stroke} strokeWidth={targetStroke} />
        <circle cx="256" cy="256" r="40" fill={stroke} />
      </svg>
    ),
    { width: size.width, height: size.height }
  );
}
