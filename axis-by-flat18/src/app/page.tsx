import { Header } from "@/components/header";
import { <PERSON> } from "@/components/hero/hero";
import { WhyAxis } from "@/components/why-axis";
import { ProcessTimeline } from "@/components/process/process-timeline";
import { FeaturedWork } from "@/components/work/featured-work";
import { PricingCards } from "@/components/pricing/pricing-cards";
import { SupportAftercare } from "@/components/support-aftercare";
import { FAQ } from "@/components/faq";
import { FinalCTA } from "@/components/final-cta";
import { Footer } from "@/components/footer";

export default function Home() {
  return (
    <div className="min-h-screen bg-[var(--bg-900)] text-[var(--text-100)]">
      <Header />
      <main id="main">
        <Hero />
        <WhyAxis />
        <ProcessTimeline />
        <FeaturedWork />
        <PricingCards />
        <SupportAftercare />
        <FAQ />
        <FinalCTA />
      </main>
      <Footer />
    </div>
  );
}
