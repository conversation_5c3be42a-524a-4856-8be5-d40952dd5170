import { Header } from "@/components/header";
import { Footer } from "@/components/footer";
import { LeadForm } from "@/components/forms/lead-form";
import { Button } from "@/components/ui/button";
import Link from "next/link";

const nextSteps = [
  {
    title: "Submit details",
    description: "2–3 minute form so we understand scope, timeline, and constraints.",
    timing: "Today",
  },
  {
    title: "Scoping call",
    description: "20-minute call to confirm scope, answer questions, and align on tier.",
    timing: "Within 24h",
  },
  {
    title: "Proposal & booking",
    description: "We send the fixed-scope proposal with payment link to secure your slot.",
    timing: "Within 48h",
  },
  {
    title: "Kickoff",
    description: "You meet the team, join the workspace, and we start delivery.",
    timing: "Next available slot",
  },
];

const assurances = [
  "Fixed scope + price locked before we begin",
  "Weekly demos and async updates",
  "Launch checklist and handover included",
];

interface StartPageProps {
  searchParams: {
    tier?: string;
    addon?: string;
  };
}

export default function StartPage({ searchParams }: StartPageProps) {
  return (
    <div className="min-h-screen bg-white text-slate-900">
      <Header />
      <main id="main" className="bg-white">
        <section className="bg-gradient-to-br from-[#eef5ff] to-white py-20">
          <div className="mx-auto max-w-[1200px] px-4 md:px-6">
            <p className="text-xs font-semibold uppercase tracking-[0.3em] text-slate-400">
              Start a project
            </p>
            <div className="mt-6 grid gap-10 lg:grid-cols-[2fr,1fr]">
              <div className="space-y-6">
                <h1 className="font-display text-4xl font-semibold leading-tight sm:text-5xl">
                  Tell us about your MVP and we’ll book your build slot.
                </h1>
                <p className="max-w-2xl text-lg text-slate-600">
                  Fixed scope. Fixed price. Delivery in weeks with the Flat 18 team. Share your context and we’ll respond within one business day with next steps.
                </p>
                <div className="flex flex-col gap-3 sm:flex-row">
                  <Button asChild className="bg-[var(--primary)] text-white hover:bg-[#266ee6]">
                    <Link href="/call">Prefer to talk first?</Link>
                  </Button>
                  <Button asChild variant="ghost" className="text-slate-700 hover:bg-slate-100">
                    <Link href="mailto:<EMAIL>">Email <EMAIL></Link>
                  </Button>
                </div>
              </div>
              <div className="space-y-4 rounded-3xl border border-slate-200 bg-white p-6 shadow-lg shadow-slate-900/10">
                <h2 className="text-sm font-semibold uppercase tracking-[0.3em] text-slate-400">
                  Assurances
                </h2>
                <ul className="space-y-3 text-sm text-slate-600">
                  {assurances.map((item) => (
                    <li key={item} className="flex items-start gap-2">
                      <span className="mt-2 inline-flex h-1.5 w-1.5 rounded-full bg-[#35e7cc]" aria-hidden />
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
                <p className="text-xs text-slate-400">We reply within one business day.</p>
              </div>
            </div>
          </div>
        </section>

        <section className="py-16">
          <div className="mx-auto grid max-w-[1200px] gap-10 px-4 md:px-6 lg:grid-cols-[1.4fr,1fr]">
            <div className="space-y-6">
              <LeadForm defaultTier={searchParams.tier} defaultAddon={searchParams.addon} />
              <p className="text-xs text-slate-500">
                By submitting this form you agree to our {" "}
                <Link href="/legal/privacy" className="underline">
                  privacy policy
                </Link>
                . We’ll only use your information to respond to your enquiry.
              </p>
            </div>
            <div className="space-y-6">
              <div className="rounded-3xl border border-slate-200 bg-slate-50 p-6">
                <h2 className="text-lg font-semibold text-slate-900">Need a quick call?</h2>
                <p className="mt-2 text-sm text-slate-600">
                  Book a 15-minute intro and we’ll help shape scope before you commit to a tier.
                </p>
                <Button asChild className="mt-4 w-full bg-[var(--primary)] text-white hover:bg-[#266ee6]">
                  <Link href="/call">Book a call</Link>
                </Button>
              </div>
              <div className="rounded-3xl border border-slate-200 bg-white p-6 shadow-sm shadow-slate-900/5">
                <h2 className="text-lg font-semibold text-slate-900">What happens next</h2>
                <ul className="mt-4 space-y-4 text-sm text-slate-600">
                  {nextSteps.map((step) => (
                    <li key={step.title} className="flex items-start gap-3">
                      <div className="rounded-full bg-slate-900/5 px-3 py-1 text-xs font-semibold text-slate-500">
                        {step.timing}
                      </div>
                      <div>
                        <p className="font-semibold text-slate-900">{step.title}</p>
                        <p className="text-sm text-slate-600">{step.description}</p>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
              <div className="rounded-3xl border border-slate-200 bg-white p-6 shadow-sm shadow-slate-900/5">
                <h2 className="text-lg font-semibold text-slate-900">Prefer chat?</h2>
                <p className="mt-2 text-sm text-slate-600">Use the chat widget bottom-right. We’ll respond when we’re online.</p>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
}
