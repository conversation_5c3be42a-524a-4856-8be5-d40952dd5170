"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { trackLeadSubmission, trackUmamiOnly } from "@/lib/twitter";

const leadFormSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  company: z.string().optional(),
  idea: z.string().min(10, "Please provide at least 10 characters describing your idea"),
  tier: z.enum(["start", "build", "pro"], { error: "Please select a tier" }),
  timeline: z.string().min(1, "Please select your preferred timeline"),
  budget: z.string().min(1, "Please select your budget range"),
});

type LeadFormData = z.infer<typeof leadFormSchema>;

interface LeadFormProps {
  defaultTier?: string;
  defaultAddon?: string;
}

export function LeadForm({ defaultTier, defaultAddon }: LeadFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const form = useForm<LeadFormData>({
    resolver: zodResolver(leadFormSchema),
    defaultValues: {
      name: "",
      email: "",
      company: "",
      idea: "",
      tier: defaultTier as "start" | "build" | "pro" | undefined,
      timeline: "",
      budget: "",
    },
  });

  const onSubmit = async (data: LeadFormData) => {
    setIsSubmitting(true);
    
    try {
      const response = await fetch("/api/lead", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...data,
          addon: defaultAddon,
        }),
      });

      if (response.ok) {
        trackLeadSubmission({ email: data.email, budget: data.budget, tier: data.tier });
        trackUmamiOnly("intake_form_submitted", { tier: data.tier, timeline: data.timeline });
        setIsSubmitted(true);
        form.reset();
      } else {
        throw new Error("Failed to submit form");
      }
    } catch (error) {
      console.error("Form submission error:", error);
      // In a real app, you'd show an error message to the user
      alert("There was an error submitting your form. Please try again or contact us directly.");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isSubmitted) {
    return (
      <Card className="mx-auto max-w-2xl border border-slate-200 shadow-sm shadow-slate-900/5">
        <CardContent className="p-8 text-center">
          <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-[#35e7cc] text-[#0b1220]">
            <span className="bi bi-check2 text-3xl" aria-hidden="true" />
          </div>
          <h2 className="font-display text-2xl font-semibold text-slate-900 mb-4">
            Thank you for your interest!
          </h2>
          <p className="mb-6 text-sm text-slate-600">
            We&apos;ve received your project details and will be in touch within 24 hours to discuss your requirements and available project slots.
          </p>
          <div className="rounded-2xl border border-slate-200 bg-slate-50 p-4 text-left text-sm text-slate-600">
            <p className="font-semibold text-slate-800">What happens next?</p>
            <ol className="mt-2 list-decimal space-y-1 pl-4">
              <li>We review your requirements</li>
              <li>Schedule a 20-minute scoping call</li>
              <li>Send a fixed-scope proposal with payment link</li>
              <li>Reserve your project slot</li>
            </ol>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="mx-auto max-w-2xl border border-slate-200 shadow-sm shadow-slate-900/5">
      <CardHeader>
        <CardTitle className="font-display text-2xl text-slate-900">
          Start Your Project
        </CardTitle>
        <CardDescription>
          Tell us about your project and we&apos;ll get back to you within 24 hours with a detailed proposal.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name *</FormLabel>
                    <FormControl>
                      <Input placeholder="Your full name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email *</FormLabel>
                    <FormControl>
                      <Input placeholder="<EMAIL>" type="email" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="company"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Company</FormLabel>
                  <FormControl>
                    <Input placeholder="Your company name (optional)" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="idea"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Project Description *</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Briefly describe your project idea, key features, and what success looks like..."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    The more detail you provide, the better we can tailor our approach.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="tier"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Preferred Tier *</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a tier" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="start">Axis Start (from £1,495, delivered in weeks)</SelectItem>
                        <SelectItem value="build">Axis Build (from £2,995, delivered in weeks)</SelectItem>
                        <SelectItem value="pro">Axis Pro (from £5,495, delivered in weeks)</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="timeline"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Preferred Timeline *</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="When do you need this?" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="asap">As soon as possible</SelectItem>
                        <SelectItem value="2-weeks">Within 2 weeks</SelectItem>
                        <SelectItem value="month">Within a month</SelectItem>
                        <SelectItem value="quarter">This quarter</SelectItem>
                        <SelectItem value="flexible">Flexible</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="budget"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Budget Range *</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select your budget range" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="start">Approximately £1,500 - £3,000</SelectItem>
                      <SelectItem value="build">Approximately £3,000 - £6,000</SelectItem>
                      <SelectItem value="pro">£6,000+</SelectItem>
                      <SelectItem value="scale">Interested in Scale Support</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {defaultAddon && (
              <div className="rounded-2xl border border-slate-200 bg-slate-50 p-4 text-sm text-slate-600">
                <p>
                  <strong>Add-on selected:</strong> {defaultAddon === "scale-support" ? "Axis Scale Support (£750/month)" : defaultAddon}
                </p>
              </div>
            )}

            <Button
              type="submit"
              size="lg"
              className="w-full"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <span className="bi bi-arrow-repeat mr-2 inline-block h-4 w-4 animate-spin align-middle" aria-hidden="true" />
                  Submitting...
                </>
              ) : (
                "Submit Project Request"
              )}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
