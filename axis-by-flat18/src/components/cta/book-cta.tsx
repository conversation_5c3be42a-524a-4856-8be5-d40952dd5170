"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { openChat } from "@/components/chatwoot";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { trackCtaClick } from "@/lib/twitter";

const EMBED_URL = process.env.NEXT_PUBLIC_BOOKING_EMBED_URL;
const BOOKING_LINK = process.env.NEXT_PUBLIC_BOOKING_LINK;

interface BookCTAProps {
  showAnonymous?: boolean;
  showCall?: boolean;
  className?: string;
}

function computeEmbedUrl(): string | undefined {
  if (EMBED_URL) return EMBED_URL;
  if (!BOOKING_LINK) return undefined;
  try {
    const url = new URL(BOOKING_LINK);
    const host = url.hostname.toLowerCase();
    // Auto-embed Cal.com links if only a public URL is provided
    if (host === "cal.com" || host.endsWith(".cal.com")) {
      const hasQuery = url.search && url.search.length > 1;
      url.search = hasQuery ? `${url.search}&embed=true` : "?embed=true";
      return url.toString();
    }
  } catch {}
  return undefined;
}

export function BookCTA({
  showAnonymous = true,
  showCall = true,
  className,
}: BookCTAProps) {
  const cardCount = [showAnonymous, showCall].filter(Boolean).length;

  if (cardCount === 0) {
    return null;
  }

  const containerWidth = cardCount > 1 ? "max-w-6xl" : "max-w-3xl";
  const columnClass = cardCount > 1 ? "lg:grid-cols-2" : "";

  return (
    <section className={cn("mb-16", className)}>
      <div className={cn("grid gap-6 mx-auto", columnClass, containerWidth)}>
        {showAnonymous && (
          <Card className="border-[var(--axis-cobalt)]/30">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-axis-text">
                <span className="bi bi-chat-dots text-[var(--axis-cobalt)]" aria-hidden="true" />
                Jump into a live chat introduction
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-[var(--text-300)] mb-4">
                Want to sanity-check your idea with the team? Start a live chat and we&apos;ll join within minutes to swap intros and talk scope.
              </p>
              <ul className="list-disc list-inside text-[var(--text-300)] mb-6 space-y-1">
                <li>Meet the Axis team instantly — no scheduling required</li>
                <li>Share the quick version of your idea and get fast feedback</li>
                <li>No email needed, conversation stays private to you</li>
              </ul>
              <Button
                onClick={() => {
                  trackCtaClick({ label: "live_chat_intro_opened", location: "book-cta", href: "chat" });
                  openChat();
                }}
                size="lg"
              >
                Start live chat
              </Button>
            </CardContent>
          </Card>
        )}

        {showCall && (
          <Card className="border-[var(--axis-cobalt)]/30">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-axis-text">
                <span className="bi bi-calendar3 text-[var(--axis-cobalt)]" aria-hidden="true" />
                Book a 15-minute call
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-[var(--text-300)] mb-4">
                Prefer to speak? Choose a slot for a short scoping call. We’ll confirm scope and next steps.
              </p>
              {computeEmbedUrl() ? (
                <div className="rounded-lg overflow-hidden border border-border">
                  <iframe
                    src={computeEmbedUrl()}
                    style={{ width: "100%", height: 640, border: 0 }}
                    loading="lazy"
                    title="Book a call"
                  />
                </div>
              ) : (
                <div className="flex items-center gap-3">
                  <Button asChild size="lg" className="flex-1">
                    <Link
                      href={BOOKING_LINK || "/call"}
                      target={BOOKING_LINK ? "_blank" : undefined}
                      onClick={() =>
                        trackCtaClick({
                          label: "cta_book_call_clicked",
                          location: "book-cta",
                          href: BOOKING_LINK || "/call",
                        })
                      }
                    >
                      Choose a time
                    </Link>
                  </Button>
                  <p className="text-xs text-[var(--text-500)]">
                    Set NEXT_PUBLIC_BOOKING_EMBED_URL for an inline calendar
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </section>
  );
}
