"use client";

import { motion } from "framer-motion";

const logos = [
  "Backed teams",
  "VC analysts",
  "Web3 ops",
  "Fintech MVPs",
  "Media startups",
  "Research labs",
];

export function TrustStrip() {
  const doubled = [...logos, ...logos];

  return (
    <section className="border-y border-slate-200 bg-white py-6">
      <div className="mx-auto max-w-[1200px] px-4 md:px-6">
        <div className="flex items-center justify-between gap-4 pb-4 text-xs font-semibold uppercase tracking-[0.3em] text-slate-400 sm:pb-0">
          Trusted by teams shipping in weeks
          <span className="sr-only">Backed teams, VC analysts, Web3 ops, Fintech MVPs, Media startups, Research labs</span>
        </div>
        <div className="relative mt-4 overflow-hidden">
          <motion.div
            className="flex min-w-[200%] gap-12"
            animate={{ x: ["0%", "-50%"] }}
            transition={{ repeat: Infinity, duration: 28, ease: "linear" }}
          >
            {doubled.map((label, index) => (
              <span
                key={`${label}-${index}`}
                className="flex h-12 min-w-[140px] items-center justify-center rounded-xl border border-slate-200 bg-slate-50 px-6 text-sm font-medium text-slate-500"
                aria-hidden
              >
                {label}
              </span>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  );
}
