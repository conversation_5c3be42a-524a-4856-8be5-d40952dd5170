"use client";

import Image from "next/image";
import Link from "next/link";
import { motion } from "framer-motion";

const productLinks = [
  { label: "Process", href: "/process" },
  { label: "Work", href: "/work" },
  { label: "Pricing", href: "/pricing" },
  { label: "Start a project", href: "/start" },
];

const companyLinks = [
  { label: "Support", href: "/support" },
  { label: "FAQ", href: "/#faq" },
  { label: "Legal", href: "/legal" },
  { label: "Book a call", href: "/call" },
];

export function Footer() {
  return (
    <footer className="bg-[var(--bg-950)] text-[var(--text-300)]">
      <div className="mx-auto max-w-[1200px] px-4 py-16 md:px-6">
        <div className="grid gap-12 lg:grid-cols-[2fr,3fr]">
          <motion.div
            className="space-y-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.4 }}
            transition={{ duration: 0.4 }}
          >
            <Link href="/" className="inline-flex items-center gap-3" aria-label="Axis by Flat 18">
              <Image
                src="/brand/axis-lockup-grey.svg"
                alt="Axis by Flat 18"
                width={200}
                height={48}
                className="h-auto w-40 md:w-48"
              />
            </Link>
            <p className="max-w-md text-sm leading-7 text-[var(--text-300)]">
              Axis is the fixed-scope, fixed-price MVP arm of Flat 18. We ship investor-ready product with transparent delivery and clean handover.
            </p>
            <div className="space-y-2 text-sm text-[var(--text-500)]">
              <p>
                Email: {" "}
                <Link
                  href="mailto:<EMAIL>"
                  className="text-[var(--text-100)] underline decoration-[var(--axis-cobalt)] decoration-2 underline-offset-4 hover:text-[var(--axis-secondary)]"
                >
                  <EMAIL>
                </Link>
              </p>
              <p>Based in WI, working globally.</p>
              <p>
                Created by the team at {" "}
                <Link
                  href="https://flat18.co.uk"
                  className="text-[var(--text-100)] underline decoration-[var(--axis-cobalt)] decoration-2 underline-offset-4 hover:text-[var(--axis-secondary)]"
                >
                  Flat 18
                </Link>
                .
              </p>
            </div>
          </motion.div>

          <div className="grid gap-8 sm:grid-cols-2 lg:grid-cols-3">
            <motion.div
              initial={{ opacity: 0, y: 24 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, amount: 0.3 }}
              transition={{ duration: 0.4 }}
            >
              <h4 className="text-sm font-semibold uppercase tracking-[0.2em] text-[var(--text-100)]">
                Product
              </h4>
              <ul className="mt-4 space-y-3 text-sm">
                {productLinks.map((link) => (
                  <li key={link.href}>
                    <Link
                      href={link.href}
                      className="inline-flex items-center gap-2 text-[var(--text-300)] transition hover:text-[var(--text-100)]"
                    >
                      {link.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 24 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, amount: 0.3 }}
              transition={{ duration: 0.4, delay: 0.05 }}
            >
              <h4 className="text-sm font-semibold uppercase tracking-[0.2em] text-[var(--text-100)]">
                Company
              </h4>
              <ul className="mt-4 space-y-3 text-sm">
                {companyLinks.map((link) => (
                  <li key={link.href}>
                    <Link
                      href={link.href}
                      className="inline-flex items-center gap-2 text-[var(--text-300)] transition hover:text-[var(--text-100)]"
                    >
                      {link.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 24 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, amount: 0.3 }}
              transition={{ duration: 0.4, delay: 0.1 }}
            >
              <h4 className="text-sm font-semibold uppercase tracking-[0.2em] text-[var(--text-100)]">
                Contact
              </h4>
              <ul className="mt-4 space-y-3 text-sm text-[var(--text-300)]">
                <li>
                  <Link
                    href="mailto:<EMAIL>"
                    className="text-[var(--text-100)] underline decoration-[var(--axis-cobalt)] decoration-2 underline-offset-4 hover:text-[var(--axis-secondary)]"
                  >
                    <EMAIL>
                  </Link>
                </li>
                <li>
                  <Link
                    href="/support"
                    className="inline-flex items-center gap-2 text-[var(--text-300)] transition hover:text-[var(--text-100)]"
                  >
                    Support & aftercare
                  </Link>
                </li>
              </ul>
            </motion.div>
          </div>
        </div>

        <div className="mt-12 flex flex-col gap-4 border-t border-[var(--border-600)] pt-6 text-sm text-[var(--text-500)] sm:flex-row sm:items-center sm:justify-between">
          <p>© {new Date().getFullYear()} Axis by Flat 18. All rights reserved.</p>
          <div className="flex items-center gap-3 text-[var(--text-500)]">
            <span>Crafted with Flat 18 standards</span>
            <span className="inline-flex h-2 w-2 rounded-full bg-[var(--axis-cobalt)]" aria-hidden />
            <span className="inline-flex h-2 w-2 rounded-full bg-[var(--axis-secondary)]" aria-hidden />
          </div>
        </div>
      </div>
    </footer>
  );
}

