"use client";

import Link from "next/link";
import <PERSON><PERSON> from "lottie-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { HeroBackdrop } from "@/components/hero/hero-backdrop";
import { trackCtaClick } from "@/lib/twitter";
import getometicPlasmaAnimation from "@/app/lottie/Getometic plasma.json";

export function Hero() {
  return (
    <section className="relative overflow-hidden bg-[var(--bg-950)]">
      <HeroBackdrop />
      <div className="relative mx-auto flex min-h-[560px] max-w-[1200px] flex-col justify-center gap-16 px-4 py-20 md:flex-row md:items-center md:px-6 md:py-28">
        <div className="relative z-10 max-w-xl space-y-8 text-[var(--text-100)]">
          <span className="inline-flex items-center gap-2 rounded-full border border-[var(--border-600)] bg-[var(--surface-700)]/70 px-4 py-1 text-xs font-semibold uppercase tracking-[0.28em] text-[var(--text-300)]">
            Axis by Flat 18
          </span>
          <div className="space-y-5">
            <h1 className="font-display text-4xl font-semibold leading-tight sm:text-5xl lg:text-[3.5rem] lg:leading-[1.05]">
              Launch a polished MVP in weeks. Fixed scope, fixed price.
            </h1>
            <p className="text-lg leading-8 text-[var(--text-300)]">
              Axis is a small, senior team building investor-ready design and production code. We’re new, operate transparently, and ship fast using proven patterns.
            </p>
          </div>
          <div className="flex flex-col gap-4 sm:flex-row">
            <Button
              size="lg"
              asChild
              className="bg-[var(--axis-cobalt)] text-[var(--bg-950)] hover:bg-[var(--axis-cobalt)]/90 focus-visible:ring-[var(--axis-secondary)]"
            >
              <Link
                href="/start"
                onClick={() =>
                  trackCtaClick({
                    label: "cta_start_project_clicked",
                    location: "hero",
                    href: "/start",
                  })
                }
                className="inline-flex items-center gap-2"
              >
                <i className="bi bi-rocket" aria-hidden />
                Start a project
              </Link>
            </Button>
            <Button
              variant="ghost"
              size="lg"
              asChild
              className="border border-[var(--axis-cobalt)]/60 bg-transparent text-[var(--text-100)] hover:bg-[var(--surface-700)] focus-visible:ring-[var(--axis-secondary)]"
            >
              <Link
                href="/call"
                onClick={() =>
                  trackCtaClick({
                    label: "cta_book_call_clicked",
                    location: "hero",
                    href: "/call",
                  })
                }
                className="inline-flex items-center gap-2"
              >
                <i className="bi bi-telephone" aria-hidden />
                Book a 15-minute call
              </Link>
            </Button>
          </div>
          <p className="text-sm text-[var(--text-500)]">
            Created by the team at {" "}
            <Link
              href="https://flat18.co.uk"
              className="text-[var(--text-100)] underline decoration-[var(--axis-cobalt)] decoration-2 underline-offset-4 hover:text-[var(--axis-secondary)]"
            >
              Flat 18
            </Link>
            .
          </p>
        </div>

        <div className="relative z-0 flex w-full justify-center md:-mr-16 md:justify-end lg:-mr-8 xl:mr-0">
            <Lottie
              animationData={getometicPlasmaAnimation}
              loop
              autoplay
              className="h-full w-full animate-hero-rotate"
              rendererSettings={{ preserveAspectRatio: "xMidYMid slice" }}
            />
        </div>
      </div>
    </section>
  );
}
