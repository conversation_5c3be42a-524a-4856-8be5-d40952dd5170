"use client";

import { useEffect, useRef } from "react";
import <PERSON>tie, { LottieRefCurrentProps } from "lottie-react";

import flupigoAnimation from "@/app/lottie/flupigo.json";

export function HeroBackdrop() {
  const lottieRef = useRef<LottieRefCurrentProps>(null);

  useEffect(() => {
    // Slow the Lottie playback speed to 0.6x
    lottieRef.current?.setSpeed(0.05);
  }, []);

  return (
    <div className="pointer-events-none absolute inset-0 overflow-hidden" aria-hidden>
      <Lottie
        lottieRef={lottieRef}
        animationData={flupigoAnimation}
        loop
        autoplay
        className="h-full w-full"
        rendererSettings={{ preserveAspectRatio: "xMidYMid slice" }}
        style={{ opacity: 0.01 }}
      />
      <div className="absolute inset-0 bg-[var(--bg-900)]/75" />
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(4,11,34,0.88)_0%,rgba(4,11,34,0.7)_45%,rgba(4,11,34,0)_80%)]" />
      <div className="absolute -top-32 right-1/3 h-72 w-72 rounded-full bg-[var(--axis-cobalt)]/40 blur-3xl" />
    </div>
  );
}
