"use client";

import Script from "next/script";

interface UmamiAnalyticsProps {
  websiteId: string;
  src?: string;
}

export function UmamiAnalytics({ websiteId, src = "https://umami.is/script.js" }: UmamiAnalyticsProps) {
  if (process.env.NODE_ENV === "development") {
    return null;
  }

  return (
    <Script
      src={src}
      data-website-id={websiteId}
      strategy="afterInteractive"
    />
  );
}

interface AckeeAnalyticsProps {
  server: string;
  domainId: string;
  src?: string;
  enabled?: boolean;
}

export function AckeeAnalytics({
  server,
  domainId,
  src = "https://master--melodic-taffy-1a4c18.netlify.app/tracker.js",
  enabled = true,
}: AckeeAnalyticsProps) {
  if (process.env.NODE_ENV === "development" || !enabled) return null;

  return (
    <Script
      async
      src={src}
      data-ackee-server={server}
      data-ackee-domain-id={domainId}
      strategy="afterInteractive"
    />
  );
}
