"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON>, CardH<PERSON><PERSON>, CardT<PERSON>le, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ChevronDown, ChevronRight, List } from "lucide-react";
import { cn } from "@/lib/utils";

interface HeadingInfo {
  id: string;
  text: string;
  level: number;
}

interface TableOfContentsProps {
  headings: HeadingInfo[];
  activeTab: string;
  mobile?: boolean;
}

export function TableOfContents({ headings, activeTab, mobile = false }: TableOfContentsProps) {
  const [activeSection, setActiveSection] = useState<string>("");
  const [isExpanded, setIsExpanded] = useState(false);

  // Track active section on scroll
  useEffect(() => {
    const handleScroll = () => {
      const headingElements = headings.map(h => document.getElementById(h.id)).filter(Boolean);
      
      // Find the heading that's currently in view
      let current = "";
      for (const element of headingElements) {
        if (element) {
          const rect = element.getBoundingClientRect();
          if (rect.top <= 100) {
            current = element.id;
          }
        }
      }
      setActiveSection(current);
    };

    handleScroll();
    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [headings]);

  const scrollToSection = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      const offset = 100; // Account for sticky header
      const elementPosition = element.getBoundingClientRect().top;
      const offsetPosition = elementPosition + window.pageYOffset - offset;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });

      // Update URL hash
      window.history.pushState(null, '', `#${activeTab}-${id}`);
    }

    // Close mobile menu
    if (mobile) {
      setIsExpanded(false);
    }
  };

  const filteredHeadings = headings.filter(heading => heading.level <= 3);

  if (filteredHeadings.length === 0) {
    return null;
  }

  if (mobile) {
    return (
      <Card className="bg-card/30 border border-border/30">
        <CardHeader className="pb-3">
          <Button
            variant="ghost"
            onClick={() => setIsExpanded(!isExpanded)}
            className="flex items-center justify-between w-full p-0 h-auto hover:bg-transparent"
          >
            <div className="flex items-center gap-2">
              <List className="w-4 h-4 text-axis-accent-1" />
              <CardTitle className="text-sm font-medium text-axis-text">
                Table of Contents
              </CardTitle>
            </div>
            {isExpanded ? (
              <ChevronDown className="w-4 h-4 text-[var(--text-500)]" />
            ) : (
              <ChevronRight className="w-4 h-4 text-[var(--text-500)]" />
            )}
          </Button>
        </CardHeader>
        
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.2 }}
            >
              <CardContent className="pt-0">
                <nav className="space-y-1">
                  {filteredHeadings.map((heading) => (
                    <button
                      key={heading.id}
                      onClick={() => scrollToSection(heading.id)}
                      className={cn(
                        "block w-full text-left text-sm py-2 px-3 rounded-md transition-all duration-200 hover:bg-axis-accent-1/10",
                        heading.level === 1 && "font-medium",
                        heading.level === 2 && "pl-6 text-sm",
                        heading.level === 3 && "pl-9 text-xs",
                        activeSection === heading.id
                          ? "text-axis-accent-1 bg-axis-accent-1/10 font-medium"
                          : "text-[var(--text-300)] hover:text-axis-text"
                      )}
                    >
                      {heading.text}
                    </button>
                  ))}
                </nav>
              </CardContent>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>
    );
  }

  // Desktop version
  return (
    <Card className="bg-card/30 border border-border/30">
      <CardHeader className="pb-3">
        <div className="flex items-center gap-2">
          <List className="w-4 h-4 text-axis-accent-1" />
          <CardTitle className="text-sm font-medium text-axis-text">
            Table of Contents
          </CardTitle>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <nav className="space-y-1">
          {filteredHeadings.map((heading) => (
            <motion.button
              key={heading.id}
              onClick={() => scrollToSection(heading.id)}
              className={cn(
                "block w-full text-left text-sm py-2 px-3 rounded-md transition-all duration-200 hover:bg-axis-accent-1/10",
                heading.level === 1 && "font-medium",
                heading.level === 2 && "pl-6 text-sm",
                heading.level === 3 && "pl-9 text-xs",
                activeSection === heading.id
                  ? "text-axis-accent-1 bg-axis-accent-1/10 font-medium border-l-2 border-axis-accent-1"
                  : "text-[var(--text-300)] hover:text-axis-text"
              )}
              whileHover={{ x: activeSection === heading.id ? 0 : 2 }}
              transition={{ duration: 0.1 }}
            >
              {heading.text}
            </motion.button>
          ))}
        </nav>
      </CardContent>
    </Card>
  );
}