"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Content } from "@/components/ui/tabs";
import { <PERSON>, <PERSON>H<PERSON>er, CardT<PERSON>le, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ChevronUp, FileText, Shield, Cookie, Accessibility, AlertTriangle } from "lucide-react";
import { parseMarkdownContent, extractLastUpdated } from "@/lib/markdown";
import { TableOfContents } from "./table-of-contents";
import { policyContent } from "@/lib/policy-content";

type TOCHeading = {
  id: string;
  text: string;
  level: number;
};

interface PolicyDocument {
  id: string;
  title: string;
  description: string;
  content: string;
  icon: React.ComponentType<{ className?: string }>;
  lastUpdated: string;
}

const policies: PolicyDocument[] = [
  {
    id: "terms",
    title: "Terms of Service",
    description: "Terms and conditions governing the use of Axis services",
    content: policyContent.terms,
    icon: FileText,
    lastUpdated: extractLastUpdated(policyContent.terms),
  },
  {
    id: "privacy",
    title: "Privacy Policy", 
    description: "How we collect, use, and protect your personal information",
    content: policyContent.privacy,
    icon: Shield,
    lastUpdated: extractLastUpdated(policyContent.privacy),
  },
  {
    id: "cookies",
    title: "Cookie Policy",
    description: "Information about cookies and tracking technologies we use",
    content: policyContent.cookies,
    icon: Cookie,
    lastUpdated: extractLastUpdated(policyContent.cookies),
  },
  {
    id: "accessibility",
    title: "Accessibility Statement",
    description: "Our commitment to digital accessibility and WCAG compliance",
    content: policyContent.accessibility,
    icon: Accessibility,
    lastUpdated: extractLastUpdated(policyContent.accessibility),
  },
  {
    id: "acceptable-use",
    title: "Acceptable Use Policy",
    description: "Guidelines for appropriate use of our services",
    content: policyContent.acceptableUse,
    icon: AlertTriangle,
    lastUpdated: extractLastUpdated(policyContent.acceptableUse),
  },
];

export function LegalContent() {
  const [activeTab, setActiveTab] = useState("terms");
  const [showScrollToTop, setShowScrollToTop] = useState(false);
  const [parsedContent, setParsedContent] = useState<{
    [key: string]: { html: React.ReactNode[]; headings: TOCHeading[] };
  }>({});

  // Parse all markdown content on mount
  useEffect(() => {
    const parsed: { [key: string]: { html: React.ReactNode[]; headings: TOCHeading[] } } = {};
    
    policies.forEach(policy => {
      parsed[policy.id] = parseMarkdownContent(policy.content);
    });
    
    setParsedContent(parsed);
  }, []);

  // Handle scroll to top button visibility
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollToTop(window.scrollY > 400);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Handle URL hash changes for deep linking
  useEffect(() => {
    const hash = window.location.hash.slice(1);
    if (hash) {
      const policyId = policies.find(p => hash.startsWith(p.id))?.id;
      if (policyId && policyId !== activeTab) {
        setActiveTab(policyId);
      }
      
      // Scroll to the specific section after a short delay
      setTimeout(() => {
        const element = document.getElementById(hash);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' });
        }
      }, 100);
    }
  }, [activeTab]);

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    // Update URL without the hash part
    window.history.pushState(null, '', `/legal#${value}`);
  };

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const activePolicyContent = parsedContent[activeTab];

  return (
    <div className="min-h-screen bg-axis-base">
      {/* Hero Section */}
      <section className="pt-24 pb-16">
        <div className="container mx-auto px-4">
          <motion.div
            className="max-w-4xl"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <div className="mb-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-2 h-8 bg-axis-accent-1 rounded-full" />
                <span className="text-axis-accent-1 text-sm font-medium uppercase tracking-wider">
                  Legal Documentation
                </span>
              </div>
              <h1 className="font-display text-4xl lg:text-5xl font-bold text-axis-text mb-4">
                Terms, Privacy & Policies
              </h1>
              <p className="text-lg text-[var(--text-300)] leading-relaxed max-w-3xl">
                Complete legal documentation for Axis by Flat 18. These policies 
                ensure transparency, protect your rights, and outline our mutual 
                responsibilities in providing rapid MVP and app development services.
              </p>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Main Content */}
      <section className="pb-16">
        <div className="container mx-auto px-4">
          <Tabs 
            value={activeTab} 
            onValueChange={handleTabChange}
            className="w-full"
          >
            {/* Mobile-first Tab List - Scrollable on mobile */}
            <div className="mb-8">
              <TabsList className="w-full h-auto p-2 bg-card/50 backdrop-blur border border-border/50 flex-wrap lg:flex-nowrap gap-2">
                {policies.map((policy) => (
                  <TabsTrigger
                    key={policy.id}
                    value={policy.id}
                    className="flex items-center gap-2 min-w-fit px-4 py-3 text-sm font-medium whitespace-nowrap data-[state=active]:bg-axis-accent-1 data-[state=active]:text-white transition-all duration-300"
                  >
                    <policy.icon className="w-4 h-4" />
                    <span className="hidden sm:inline">{policy.title}</span>
                    <span className="sm:hidden">{policy.title.split(' ')[0]}</span>
                  </TabsTrigger>
                ))}
              </TabsList>
            </div>

            {/* Policy Content */}
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
              {/* Table of Contents - Desktop Sidebar */}
              <div className="hidden lg:block lg:col-span-1">
                <div className="sticky top-24">
                  {activePolicyContent && (
                    <TableOfContents 
                      headings={activePolicyContent.headings}
                      activeTab={activeTab}
                    />
                  )}
                </div>
              </div>

              {/* Main Content Area */}
              <div className="lg:col-span-3">
                {policies.map((policy) => (
                  <TabsContent 
                    key={policy.id} 
                    value={policy.id}
                    className="mt-0 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-axis-accent-1 focus-visible:ring-offset-2 focus-visible:ring-offset-axis-base rounded-lg"
                  >
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5 }}
                    >
                      <Card className="bg-card/50 backdrop-blur border border-border/50">
                        <CardHeader className="pb-6">
                          <div className="flex items-start gap-4">
                            <div className="p-3 bg-axis-accent-1/10 rounded-lg border border-axis-accent-1/20">
                              <policy.icon className="w-6 h-6 text-axis-accent-1" />
                            </div>
                            <div className="flex-1">
                              <CardTitle className="text-2xl font-display font-bold text-axis-text mb-2">
                                {policy.title}
                              </CardTitle>
                              <p className="text-[var(--text-300)] mb-4">
                                {policy.description}
                              </p>
                              <div className="text-sm text-[var(--text-500)]">
                                Last updated: {policy.lastUpdated}
                              </div>
                            </div>
                          </div>
                        </CardHeader>
                        
                        <CardContent className="prose prose-invert prose-axis max-w-none">
                          {/* Mobile Table of Contents */}
                          <div className="lg:hidden mb-8">
                            {parsedContent[policy.id] && (
                              <TableOfContents 
                                headings={parsedContent[policy.id].headings}
                                activeTab={activeTab}
                                mobile
                              />
                            )}
                          </div>

                          {/* Rendered Content */}
                          <div className="legal-content">
                            {parsedContent[policy.id]?.html}
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  </TabsContent>
                ))}
              </div>
            </div>
          </Tabs>
        </div>
      </section>

      {/* Scroll to Top Button */}
      {showScrollToTop && (
        <motion.div
          className="fixed bottom-6 right-6 z-50"
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0 }}
        >
          <Button
            onClick={scrollToTop}
            size="sm"
            className="h-12 w-12 rounded-full bg-axis-accent-1 hover:bg-axis-accent-1/90 text-white shadow-lg"
            aria-label="Scroll to top"
          >
            <ChevronUp className="w-5 h-5" />
          </Button>
        </motion.div>
      )}

      {/* Print Styles */}
      <style jsx global>{`
        @media print {
          .sticky { position: static !important; }
          .fixed { display: none !important; }
          .backdrop-blur { backdrop-filter: none !important; }
          .bg-axis-base { background: white !important; }
          .text-axis-text { color: black !important; }
          .text-axis-text\\/80 { color: #333 !important; }
          .text-axis-text\\/70 { color: #666 !important; }
          .text-axis-text\\/60 { color: #999 !important; }
          .border-border { border-color: #ddd !important; }
          .bg-card { background: white !important; }
          .legal-content { font-size: 12px !important; line-height: 1.4 !important; }
          .legal-content h1 { font-size: 18px !important; margin: 16px 0 8px 0 !important; }
          .legal-content h2 { font-size: 16px !important; margin: 14px 0 6px 0 !important; }
          .legal-content h3 { font-size: 14px !important; margin: 12px 0 4px 0 !important; }
          .legal-content p { margin: 6px 0 !important; }
          .legal-content ul { margin: 8px 0 !important; padding-left: 16px !important; }
          .legal-content li { margin: 2px 0 !important; }
          page-break-inside: avoid;
        }
      `}</style>
    </div>
  );
}
