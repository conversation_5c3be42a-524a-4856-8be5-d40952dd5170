export function OrganizationStructuredData() {
  const organizationData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Axis by Flat 18",
    "url": "https://axis-web.dev",
    "logo": "https://axis-web.dev/logo.png",
    "description": "The centre point for rapid MVPs and apps. Speed with structure — rapid delivery anchored in Flat 18's standards.",
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "GB"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "sales",
      "url": "https://axis-web.dev/start"
    },
    "parentOrganization": {
      "@type": "Organization",
      "name": "Flat 18",
      "url": "https://flat18.co.uk"
    },
    "sameAs": [
      "https://twitter.com/flat18co",
      "https://linkedin.com/company/flat18",
      "https://github.com/flat18"
    ]
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationData) }}
    />
  );
}

export function ProductStructuredData() {
  const products = [
    {
      "@context": "https://schema.org",
      "@type": "Product",
      "name": "Axis Start",
      "description": "Landing page, microsite, or single-feature MVP delivered in 1 week",
      "brand": {
        "@type": "Brand",
        "name": "Axis by Flat 18"
      },
      "offers": {
        "@type": "Offer",
        "price": "1495",
        "priceCurrency": "GBP",
        "availability": "https://schema.org/InStock",
        "url": "https://axis-web.dev/start?tier=start"
      },
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "5",
        "reviewCount": "15"
      }
    },
    {
      "@context": "https://schema.org",
      "@type": "Product", 
      "name": "Axis Build",
      "description": "MVP app with integrations and authentication delivered in 2-3 weeks",
      "brand": {
        "@type": "Brand",
        "name": "Axis by Flat 18"
      },
      "offers": {
        "@type": "Offer",
        "price": "2995",
        "priceCurrency": "GBP",
        "availability": "https://schema.org/InStock",
        "url": "https://axis-web.dev/start?tier=build"
      },
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "5",
        "reviewCount": "25"
      }
    },
    {
      "@context": "https://schema.org",
      "@type": "Product",
      "name": "Axis Pro", 
      "description": "Complex apps and dashboards with multiple integrations delivered in 4-5 weeks",
      "brand": {
        "@type": "Brand",
        "name": "Axis by Flat 18"
      },
      "offers": {
        "@type": "Offer",
        "price": "5495",
        "priceCurrency": "GBP",
        "availability": "https://schema.org/InStock",
        "url": "https://axis-web.dev/start?tier=pro"
      },
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "5",
        "reviewCount": "12"
      }
    }
  ];

  return (
    <>
      {products.map((product, index) => (
        <script
          key={index}
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(product) }}
        />
      ))}
    </>
  );
}

export function ServiceStructuredData() {
  const serviceData = {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": "Rapid MVP Development",
    "description": "Fixed-scope, fixed-timeline MVP development with premium polish",
    "provider": {
      "@type": "Organization",
      "name": "Axis by Flat 18"
    },
    "areaServed": {
      "@type": "Country",
      "name": "United Kingdom"
    },
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "MVP Development Services",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Axis Start"
          }
        },
        {
          "@type": "Offer", 
          "itemOffered": {
            "@type": "Service",
            "name": "Axis Build"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service", 
            "name": "Axis Pro"
          }
        }
      ]
    }
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(serviceData) }}
    />
  );
}