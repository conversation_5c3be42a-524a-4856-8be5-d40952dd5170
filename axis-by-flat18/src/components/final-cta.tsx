"use client";

import Link from "next/link";
import { motion } from "framer-motion";

import { Button } from "@/components/ui/button";

export function FinalCTA() {
  return (
    <section className="relative overflow-hidden bg-[var(--bg-950)] py-16">
      <div className="pointer-events-none absolute inset-0" aria-hidden>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_top_left,rgba(47,129,247,0.2),rgba(0,224,184,0.15)_45%,rgba(11,15,20,0)_80%)]" />
        <div className="absolute inset-0 bg-[var(--bg-950)]/60" />
      </div>
      <motion.div
        className="relative mx-auto max-w-[960px] rounded-[var(--radius-lg)] border border-[var(--border-600)] bg-[var(--surface-700)]/90 p-10 text-center text-[var(--text-100)] shadow-[var(--elev-1)] backdrop-blur"
        initial={{ opacity: 0, y: 24 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        viewport={{ once: true }}
      >
        <h2 className="font-display text-3xl font-semibold sm:text-4xl">Ready to start?</h2>
        <p className="mt-3 text-base leading-7 text-[var(--text-300)]">
          Tell us about your build or book time to scope it live with the team.
        </p>
        <div className="mt-8 flex flex-col items-center justify-center gap-3 sm:flex-row">
          <Button
            asChild
            size="lg"
            className="inline-flex items-center gap-2 bg-[var(--axis-cobalt)] text-[var(--bg-950)] hover:bg-[var(--axis-cobalt)]/90 focus-visible:ring-[var(--axis-secondary)]"
          >
            <Link href="/start">
              <i className="bi bi-rocket" aria-hidden />
              Start a project
            </Link>
          </Button>
          <Button
            asChild
            variant="ghost"
            size="lg"
            className="inline-flex items-center gap-2 border border-[var(--axis-cobalt)]/60 bg-transparent text-[var(--text-100)] hover:bg-[var(--surface-700)] focus-visible:ring-[var(--axis-secondary)]"
          >
            <Link href="/call">
              <i className="bi bi-telephone" aria-hidden />
              Book a 15-minute call
            </Link>
          </Button>
        </div>
      </motion.div>
    </section>
  );
}

