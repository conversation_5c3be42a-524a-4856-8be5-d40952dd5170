"use client";

import Image from "next/image";
import Link from "next/link";
import { useEffect, useMemo, useState } from "react";
import { usePathname } from "next/navigation";

import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

const navItems = [
  { label: "Process", href: "/process" },
  { label: "Work", href: "/work" },
  { label: "Pricing", href: "/pricing" },
  { label: "FAQ", href: "/#faq" },
];

const mobileQuickLinks = [
  { label: "Pricing", href: "/pricing" },
  { label: "Process", href: "/process" },
  { label: "Work", href: "/work" },
];

export function Header() {
  const pathname = usePathname();
  const [mobileOpen, setMobileOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => setScrolled(window.scrollY > 8);
    handleScroll();
    window.addEventListener("scroll", handleScroll, { passive: true });
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  useEffect(() => {
    if (mobileOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }
    return () => {
      document.body.style.overflow = "";
    };
  }, [mobileOpen]);

  const activeKey = useMemo(() => {
    if (!pathname) return null;
    const match = navItems.find((item) => {
      if (item.href === "/#faq") {
        return pathname === "/";
      }
      return pathname.startsWith(item.href);
    });
    return match?.href ?? null;
  }, [pathname]);

  const openCommandPalette = () => {
    window.dispatchEvent(new CustomEvent("axis:command-palette-open"));
  };

  return (
    <header
      role="banner"
      className={cn(
        "sticky top-0 z-50 w-full border-b border-transparent transition-all duration-300",
        mobileOpen
          ? "border-[var(--border-600)] bg-[var(--bg-950)] backdrop-blur"
          : scrolled
            ? "border-[var(--border-600)] bg-[var(--bg-950)]/92 backdrop-blur"
            : "bg-[var(--bg-950)]/70 backdrop-blur-sm"
      )}
    >
      <div className="mx-auto flex h-16 max-w-[1200px] items-center justify-between px-4 md:h-20 md:px-6">
        <Link
          href="/"
          className="flex items-center gap-3 text-[var(--text-100)]"
          aria-label="Axis by Flat 18 home"
        >
          <Image
            src="/brand/axis-lockup-grey.svg"
            alt="Axis by Flat 18"
            width={152}
            height={40}
            priority
            className="h-auto w-32 md:w-40"
          />
        </Link>

        <nav className="hidden lg:flex items-center gap-2" aria-label="Primary">
          {navItems.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                "rounded-full px-4 py-2 text-sm font-medium text-[var(--text-300)] transition-colors",
                activeKey === item.href
                  ? "bg-[var(--surface-700)] text-[var(--text-100)]"
                  : "hover:text-[var(--text-100)]"
              )}
            >
              {item.label}
            </Link>
          ))}
        </nav>

        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            className="hidden lg:inline-flex items-center gap-2 border-transparent text-[var(--text-300)] hover:bg-[var(--surface-700)]/80 hover:text-[var(--text-100)]"
            onClick={openCommandPalette}
          >
            <i className="bi bi-command" aria-hidden />
            ⌘K
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="hidden md:inline-flex items-center gap-2 border border-[var(--border-600)] bg-transparent text-[var(--text-300)] hover:bg-[var(--surface-700)] hover:text-[var(--text-100)]"
            asChild
          >
            <Link href="/call">
              <i className="bi bi-telephone" aria-hidden />
              Book a 15-minute call
            </Link>
          </Button>
          <Button
            size="sm"
            className="hidden md:inline-flex items-center gap-2 bg-[var(--axis-cobalt)] text-[var(--bg-950)] hover:bg-[var(--axis-cobalt)]/90 focus-visible:ring-[var(--axis-secondary)]"
            asChild
          >
            <Link href="/start">
              <i className="bi bi-rocket" aria-hidden />
              Start a project
            </Link>
          </Button>
          <button
            type="button"
            className="inline-flex h-10 w-10 items-center justify-center rounded-full border border-[var(--border-600)] text-[var(--text-100)] transition hover:bg-[var(--surface-700)] focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[var(--axis-secondary)] focus-visible:ring-offset-2 focus-visible:ring-offset-[var(--bg-900)] lg:hidden"
            onClick={() => setMobileOpen((prev) => !prev)}
            aria-label={mobileOpen ? "Close menu" : "Open menu"}
            aria-expanded={mobileOpen}
            aria-controls="axis-mobile-nav"
          >
            <i className={cn("text-lg", mobileOpen ? "bi bi-x" : "bi bi-list") } aria-hidden />
          </button>
        </div>
      </div>

      {mobileOpen && (
        <div className="fixed inset-0 z-40 lg:hidden">
          <div
            className="absolute inset-0 bg-black/50 backdrop-blur-sm transition-opacity"
            onClick={() => setMobileOpen(false)}
          />
          <aside
            id="axis-mobile-nav"
            aria-label="Mobile navigation"
            className="absolute right-0 top-0 h-full w-80 max-w-full bg-[var(--bg-950)] bg-[#0b0f14] text-[var(--text-100)] shadow-lg border-l border-[var(--border-600)] transform transition-transform duration-300"
          >
            <div className="flex items-center justify-between px-6 py-6 border-b border-[var(--border-600)] bg-[#0b0f14]">
              <Link href="/" className="flex items-center gap-3" onClick={() => setMobileOpen(false)}>
                <Image src="/brand/axis-icon-grey.svg" alt="Axis" width={32} height={32}/>
                <span className="text-base font-semibold">Axis by Flat 18</span>
              </Link>
              <button
                type="button"
                className="inline-flex h-10 w-10 items-center justify-center rounded-full border border-[var(--border-600)] hover:bg-[var(--surface-700)]"
                onClick={() => setMobileOpen(false)}
              >
                <i className="bi bi-x" aria-hidden />
              </button>
            </div>

            <div className="px-6 py-4 border-b border-[var(--border-600)] bg-[#0b0f14]">
              <div className="flex gap-3 overflow-x-auto">
                {mobileQuickLinks.map((link) => (
                  <Link
                    key={link.href}
                    href={link.href}
                    className="whitespace-nowrap rounded-full border border-[var(--border-600)] bg-[var(--surface-700)]/60 px-4 py-2 text-sm font-medium text-[var(--text-300)] hover:text-[var(--text-100)]"
                    onClick={() => setMobileOpen(false)}
                  >
                    {link.label}
                  </Link>
                ))}
              </div>
            </div>

            <nav className="flex-1 overflow-y-auto px-6 py-6 bg-[#0b0f14]" aria-label="Mobile primary">
              <ul className="space-y-3">
                {navItems.map((item) => (
                  <li key={item.href}>
                    <Link
                      href={item.href}
                      className={cn(
                        "block rounded-lg border px-4 py-4 text-lg font-semibold transition",
                        activeKey === item.href
                          ? "bg-[var(--surface-700)] text-[var(--text-100)] border-[var(--border-600)]"
                          : "bg-[var(--bg-900)] text-[var(--text-300)] hover:bg-[var(--surface-700)] hover:text-[var(--text-100)] border-[var(--border-600)]"
                      )}
                      onClick={() => setMobileOpen(false)}
                    >
                      {item.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </nav>

            <div className="px-6 py-6 border-t border-[var(--border-600)] space-y-4 bg-[#0b0f14]">
              <Button
                variant="ghost"
                className="w-full justify-center border border-[var(--border-600)] text-[var(--text-100)] hover:bg-[var(--surface-700)]"
                asChild
              >
                <Link href="/call" onClick={() => setMobileOpen(false)}>
                  Book a 15-minute call
                </Link>
              </Button>
              <Button
                className="w-full justify-center bg-[var(--axis-cobalt)] text-[var(--bg-950)] hover:bg-[var(--axis-cobalt)]/90"
                asChild
              >
                <Link href="/start" onClick={() => setMobileOpen(false)}>
                  Start a project
                </Link>
              </Button>
            </div>
          </aside>
        </div>
      )}
    </header>
  );
}
