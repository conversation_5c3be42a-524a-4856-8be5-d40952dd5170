"use client";

import { motion } from "framer-motion";
import Link from "next/link";

const principles = [
  "Design reviews with Flat 18 leads before anything ships",
  "Working code in a shared repo from day one",
  "Monitoring and launch checklists ready before go-live",
];

const assurances = [
  "Honest about being new: we show you what exists and what’s in motion.",
  "Transparent about scope and risks at every checkpoint.",
  "Escalation path back into the wider Flat 18 team if you need more firepower.",
];

export function SocialProof() {
  return (
    <section
      id="proof"
      className="bg-[var(--bg-800)] py-20"
      style={{ contentVisibility: "auto", containIntrinsicSize: "1px 720px" }}
    >
      <div className="mx-auto max-w-[1200px] px-4">
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 16 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          <span className="inline-flex items-center gap-2 rounded-full bg-[var(--axis-cobalt)]/10 px-4 py-2 text-xs font-semibold uppercase tracking-widest text-[var(--axis-cobalt)]">
            From the Flat 18 team
          </span>
          <h2 className="mt-4 font-display text-3xl font-semibold text-[var(--text-100)] sm:text-4xl">
            Proof in the way we work, not vanity metrics
          </h2>
          <p className="mt-3 max-w-[680px] mx-auto text-base leading-7 text-[var(--text-300)]">
            Axis is new, but the people building it have shipped product together inside Flat 18 for years. Here’s what that means when you work with us.
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 24 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          viewport={{ once: true }}
          className="mt-12 grid gap-8 lg:grid-cols-2"
        >
          <div className="rounded-[var(--radius-lg)] border border-[var(--border-600)] bg-[var(--surface-700)] p-8 text-[var(--text-300)] shadow-[var(--elev-1)]">
            <p className="text-sm uppercase tracking-[0.3em] text-[var(--axis-secondary)]">
              Standards we bring over
            </p>
            <ul className="mt-4 space-y-3 text-base leading-7">
              {principles.map((item) => (
                <li key={item} className="flex gap-3">
                  <span className="mt-2 inline-block h-1.5 w-1.5 rounded-full bg-[var(--axis-cobalt)]" aria-hidden="true" />
                  {item}
                </li>
              ))}
            </ul>
          </div>

          <div className="rounded-[var(--radius-lg)] border border-[var(--border-600)] bg-[var(--surface-700)] p-8 text-[var(--text-300)] shadow-[var(--elev-1)]">
            <p className="text-sm uppercase tracking-[0.3em] text-[var(--axis-secondary)]">
              How we handle trust
            </p>
            <ul className="mt-4 space-y-3 text-base leading-7">
              {assurances.map((item) => (
                <li key={item} className="flex gap-3">
                  <span className="mt-2 inline-block h-1.5 w-1.5 rounded-full bg-[var(--axis-cobalt)]" aria-hidden="true" />
                  {item}
                </li>
              ))}
            </ul>
            <p className="mt-6 text-sm text-[var(--text-500)]">
              Want to see more? Explore <Link href="https://flat18.co.uk/" className="underline decoration-[var(--axis-cobalt)] decoration-2 underline-offset-4 hover:text-[var(--text-100)]">Flat 18</Link> for the broader portfolio and approach.
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
