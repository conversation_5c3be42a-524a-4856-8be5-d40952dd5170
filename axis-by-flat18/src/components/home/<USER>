"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Clock3, LayoutGrid, Layers, Rocket } from "lucide-react";

const steps = [
  {
    id: "kickoff",
    title: "Kickoff",
    duration: "Day 0",
    icon: LayoutGrid,
    summary: "We scope fast with stakeholders, outline success metrics, and agree artefacts.",
    details: [
      "Workshop to define problem, target user, and success signals",
      "Documented scope, timeline, and decisions",
      "Shared workspace and comms cadence confirmed",
    ],
  },
  {
    id: "design",
    title: "Design",
    duration: "Days 1–5",
    icon: Clock3,
    summary: "Rapid wireframes and UI to validate flows before build.",
    details: [
      "Clickable wireframes inside Figma",
      "System tokens and component inventory",
      "Copy deck with single-message headlines",
    ],
  },
  {
    id: "build",
    title: "Build",
    duration: "Days 6–20",
    icon: Layers,
    summary: "Production Next.js build with weekly demos and async updates.",
    details: [
      "Feature-complete branch with QA checklists",
      "Analytics, monitoring, and error alerts wired",
      "Accessibility and performance budgets tracked",
    ],
  },
  {
    id: "launch",
    title: "Launch",
    duration: "Days 21–28",
    icon: Rocket,
    summary: "Structured release, docs, and ownership handover.",
    details: [
      "Deploy to your cloud with rollback plan",
      "Runbooks and environment variables delivered",
      "30-day stabilisation window with support notes",
    ],
  },
];

export function HowWeWork() {
  const [activeStep, setActiveStep] = useState<string>(steps[0].id);
  const current = steps.find((step) => step.id === activeStep) ?? steps[0];

  return (
    <section className="bg-slate-50 py-20" id="process">
      <div className="mx-auto max-w-[1200px] px-4 md:px-6">
        <div className="mb-12 text-center">
          <p className="text-xs font-semibold uppercase tracking-[0.3em] text-slate-400">
            How we work
          </p>
          <h2 className="mt-3 font-display text-3xl font-semibold text-slate-900 sm:text-4xl">
            A four-step path from scope to launch
          </h2>
          <p className="mx-auto mt-4 max-w-2xl text-lg text-slate-600">
            Structured weekly beats, transparent artefacts, and a launch-ready handover—so you know exactly where things stand.
          </p>
        </div>

        <div className="hidden gap-6 lg:grid">
          <div className="grid grid-cols-4 gap-4">
            {steps.map((step) => {
              const Icon = step.icon;
              const selected = step.id === activeStep;
              return (
                <button
                  key={step.id}
                  type="button"
                  onClick={() => setActiveStep(step.id)}
                  className={`group relative flex flex-col items-start gap-3 rounded-3xl border px-5 py-6 text-left transition ${
                    selected
                      ? "border-[#2f81f7] bg-white shadow-lg shadow-slate-900/10"
                      : "border-transparent bg-white/70 hover:border-slate-200 hover:bg-white"
                  }`}
                >
                  <span className={`inline-flex h-10 w-10 items-center justify-center rounded-2xl bg-slate-900/5 text-slate-700 ${selected ? "bg-[#2f81f7]/10 text-[#2f81f7]" : ""}`}>
                    <Icon className="h-5 w-5" aria-hidden />
                  </span>
                  <div>
                    <p className="text-xs font-semibold uppercase tracking-[0.3em] text-slate-400">
                      {step.duration}
                    </p>
                    <h3 className="mt-1 text-lg font-semibold text-slate-900">{step.title}</h3>
                    <p className="mt-2 text-sm text-slate-600">{step.summary}</p>
                  </div>
                  <span
                    className={`absolute inset-x-6 bottom-0 h-1 rounded-full bg-transparent transition ${selected ? "bg-[#2f81f7]" : "bg-slate-200 group-hover:bg-slate-300"}`}
                    aria-hidden
                  />
                </button>
              );
            })}
          </div>
          <div className="overflow-hidden rounded-3xl border border-slate-200 bg-white shadow-lg shadow-slate-900/10">
            <AnimatePresence mode="wait">
              <motion.div
                key={current.id}
                initial={{ opacity: 0, y: 16 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -16 }}
                transition={{ duration: 0.4 }}
                className="grid grid-cols-12 gap-10 px-10 py-12"
              >
                <div className="col-span-5 space-y-3">
                  <p className="text-xs font-semibold uppercase tracking-[0.3em] text-slate-400">
                    {current.duration}
                  </p>
                  <h3 className="text-2xl font-semibold text-slate-900">{current.title}</h3>
                  <p className="text-base text-slate-600">{current.summary}</p>
                </div>
                <ul className="col-span-7 space-y-3 text-sm text-slate-600">
                  {current.details.map((detail) => (
                    <li key={detail} className="flex items-start gap-3">
                      <span className="mt-2 inline-flex h-1.5 w-1.5 rounded-full bg-[#35e7cc]" aria-hidden />
                      <span>{detail}</span>
                    </li>
                  ))}
                </ul>
              </motion.div>
            </AnimatePresence>
          </div>
        </div>

        <div className="space-y-4 lg:hidden">
          {steps.map((step) => {
            const Icon = step.icon;
            const open = step.id === activeStep;
            return (
              <div
                key={step.id}
                className="overflow-hidden rounded-3xl border border-slate-200 bg-white shadow-sm shadow-slate-900/10"
              >
                <button
                  type="button"
                  onClick={() => setActiveStep((prev) => (prev === step.id ? "" : step.id))}
                  className="flex w-full items-center justify-between gap-4 px-6 py-5 text-left"
                >
                  <div className="flex items-center gap-4">
                    <span className="inline-flex h-10 w-10 items-center justify-center rounded-2xl bg-slate-900/5 text-slate-700">
                      <Icon className="h-5 w-5" aria-hidden />
                    </span>
                    <div>
                      <p className="text-xs font-semibold uppercase tracking-[0.3em] text-slate-400">
                        {step.duration}
                      </p>
                      <p className="mt-1 text-base font-semibold text-slate-900">{step.title}</p>
                    </div>
                  </div>
                  <span className="text-2xl text-slate-400" aria-hidden>
                    {open ? "–" : "+"}
                  </span>
                </button>
                <AnimatePresence initial={false}>
                  {open ? (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: "auto", opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="px-6 pb-6"
                    >
                      <p className="text-sm text-slate-600">{step.summary}</p>
                      <ul className="mt-4 space-y-2 text-sm text-slate-600">
                        {step.details.map((detail) => (
                          <li key={detail} className="flex items-start gap-3">
                            <span className="mt-2 inline-flex h-1.5 w-1.5 rounded-full bg-[#2f81f7]" aria-hidden />
                            <span>{detail}</span>
                          </li>
                        ))}
                      </ul>
                    </motion.div>
                  ) : null}
                </AnimatePresence>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
