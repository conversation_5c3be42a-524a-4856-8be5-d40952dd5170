"use client";

import Image from "next/image";
import Link from "next/link";
import { motion } from "framer-motion";
import { ArrowUpRight } from "lucide-react";

const workItems = [
  {
    title: "DeFi portfolio intelligence",
    description: "Unified multi-chain view with live metrics and treasury reporting.",
    image: "/placeholders/img/mockup-1.png",
    useCase: "Crypto funds",
    stack: ["Next.js", "TypeScript", "Supabase"],
    timeline: "3 weeks",
  },
  {
    title: "SaaS conversion system",
    description: "Pricing experiments, onboarding loops, and waitlist flows for pre-launch traction.",
    image: "/placeholders/img/mockup-2.png",
    useCase: "B2B SaaS",
    stack: ["Next.js", "Postgres", "Segment"],
    timeline: "2 weeks",
  },
  {
    title: "Ops automation dashboard",
    description: "Admin tooling and approvals for operational teams with audit trails built in.",
    image: "/placeholders/img/mockup-3.png",
    useCase: "Operations",
    stack: ["React", "Node", "<PERSON><PERSON>"],
    timeline: "4 weeks",
  },
];

export function WorkShowcase() {
  return (
    <section id="case-studies" className="bg-white py-20">
      <div className="mx-auto max-w-[1200px] px-4 md:px-6">
        <div className="mb-12 flex flex-col gap-6 text-center sm:text-left sm:flex-row sm:items-end sm:justify-between">
          <div>
            <p className="text-xs font-semibold uppercase tracking-[0.3em] text-slate-400">
              Work that shows how we build
            </p>
            <h2 className="mt-3 font-display text-3xl font-semibold text-slate-900 sm:text-4xl">
              Library of demo builds, refreshed monthly
            </h2>
            <p className="mt-4 max-w-2xl text-lg text-slate-600">
              Each case outlines problem, constraints, approach, and handover. Filter the full archive on the Work page.
            </p>
          </div>
          <Link
            href="/work"
            className="inline-flex items-center gap-2 self-center text-sm font-semibold text-[var(--primary)] hover:text-[#1b66f6]"
          >
            View the work archive
            <ArrowUpRight className="h-4 w-4" aria-hidden />
          </Link>
        </div>

        <div className="grid gap-6 lg:grid-cols-3">
          {workItems.map((item, index) => (
            <motion.article
              key={item.title}
              initial={{ opacity: 0, y: 24 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, amount: 0.3 }}
              transition={{ duration: 0.5, delay: index * 0.05 }}
              className="group relative overflow-hidden rounded-3xl border border-slate-200 bg-white shadow-sm shadow-slate-900/10"
            >
              <div className="relative h-56 overflow-hidden">
                <Image
                  src={item.image}
                  alt={item.title}
                  fill
                  className="object-cover transition duration-500 group-hover:scale-105"
                  sizes="(min-width: 1024px) 360px, (min-width: 640px) 50vw, 100vw"
                  priority={index === 0}
                />
                <div className="absolute inset-x-0 bottom-0 flex items-center justify-between px-6 py-3 text-xs font-semibold uppercase tracking-[0.3em] text-white/70">
                  <span>{item.useCase}</span>
                  <span>{item.timeline}</span>
                </div>
                <div className="absolute inset-0 bg-gradient-to-t from-slate-900/80 via-transparent to-transparent opacity-0 transition group-hover:opacity-100" aria-hidden />
              </div>
              <div className="space-y-4 px-6 py-6">
                <h3 className="text-xl font-semibold text-slate-900">{item.title}</h3>
                <p className="text-sm text-slate-600">{item.description}</p>
                <div className="flex flex-wrap gap-2">
                  {item.stack.map((tag) => (
                    <span key={tag} className="rounded-full bg-slate-100 px-3 py-1 text-xs font-medium text-slate-600">
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
              <div className="absolute inset-0 flex flex-col justify-end bg-gradient-to-t from-slate-900/90 via-slate-900/20 to-transparent p-6 opacity-0 transition group-hover:opacity-100">
                <p className="text-sm text-white/80">{item.description}</p>
                <Link
                  href="/work"
                  className="mt-4 inline-flex items-center gap-2 text-sm font-semibold text-white"
                >
                  View case study
                  <ArrowUpRight className="h-4 w-4" aria-hidden />
                </Link>
              </div>
            </motion.article>
          ))}
        </div>
      </div>
    </section>
  );
}
