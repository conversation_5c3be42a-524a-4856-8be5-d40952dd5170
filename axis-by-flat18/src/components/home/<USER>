"use client";

import { motion } from "framer-motion";
import { CheckCircle2, Box, Rocket } from "lucide-react";

const features = [
  {
    title: "Fixed scope",
    description: "We agree the deliverables up front, lock scope, and work inside a structured weekly rhythm.",
    icon: Rocket,
    accent: "from-[#2f81f7] to-[#aacbff]",
  },
  {
    title: "Premium polish",
    description: "UI, copy, and motion that feel production ready. Every build ships with QA, docs, and tracking.",
    icon: CheckCircle2,
    accent: "from-[#35e7cc] to-[#b9fff1]",
  },
  {
    title: "Production ready",
    description: "Foundational architecture, tests, and handover so your team—or investors—can trust the release.",
    icon: Box,
    accent: "from-[#6f75ff] to-[#dadcff]",
  },
];

export function AxisInSixty() {
  return (
    <section className="bg-white py-20">
      <div className="mx-auto max-w-[1200px] px-4 md:px-6">
        <div className="mb-12 text-center">
          <p className="text-xs font-semibold uppercase tracking-[0.3em] text-slate-400">
            Axis in 60 seconds
          </p>
          <h2 className="mt-3 font-display text-3xl font-semibold text-slate-900 sm:text-4xl">
            The MVP partner built for proof and polish
          </h2>
          <p className="mx-auto mt-4 max-w-2xl text-lg text-slate-600">
            Three commitments every project receives: clarity on scope, a disciplined build, and a runway-ready launch.
          </p>
        </div>

        <div className="grid gap-6 lg:grid-cols-3">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 24 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true, amount: 0.3 }}
                transition={{ duration: 0.5, delay: index * 0.05 }}
                className="relative overflow-hidden rounded-3xl border border-slate-200 bg-gradient-to-br from-white to-slate-50 p-8 shadow-sm shadow-slate-900/5"
              >
                <div className={`absolute inset-x-4 top-4 h-24 rounded-3xl bg-gradient-to-r ${feature.accent} opacity-40 blur-3xl`} aria-hidden />
                <div className="relative isolate flex h-12 w-12 items-center justify-center rounded-2xl bg-white shadow-md shadow-slate-900/10">
                  <Icon className="h-6 w-6 text-slate-900" aria-hidden />
                </div>
                <h3 className="relative mt-6 text-xl font-semibold text-slate-900">{feature.title}</h3>
                <p className="relative mt-3 text-sm leading-relaxed text-slate-600">{feature.description}</p>
              </motion.div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
