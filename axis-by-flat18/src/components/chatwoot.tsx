"use client";

import { useEffect } from "react";

interface ChatwootWidgetProps {
  websiteToken?: string;
  baseUrl?: string;
  enabled?: boolean;
}

declare global {
  interface Window {
    chatwootSDK?: { run: (opts: { websiteToken: string; baseUrl: string } & Record<string, unknown>) => void };
    chatwoot?: { toggle: () => void };
    $chatwoot?: {
      toggle?: () => void;
      setTheme?: (theme: "dark" | "light") => void;
      setColorTheme?: (theme: "dark" | "light") => void;
      setDarkMode?: (enabled: boolean) => void;
    };
    chatwootSettings?: Partial<{
      darkMode: string;
      dark_mode: string;
      theme: string;
      colorScheme: string;
    }>;
  }
}

function ensureChatwootDark() {
  try {
    // Try every known API surface; all are safe no-ops if missing.
    window.$chatwoot?.setTheme?.("dark");
    window.$chatwoot?.setColorTheme?.("dark");
    window.$chatwoot?.setDarkMode?.(true);
  } catch {}
  try {
    // Persisted hints used by some builds
    localStorage.setItem("cw:theme", "dark");
    localStorage.setItem("chatwoot:theme", "dark");
    localStorage.setItem("theme", "dark");
  } catch {}
}

export function ChatwootWidget({ websiteToken, baseUrl, enabled = true }: ChatwootWidgetProps) {
  useEffect(() => {
    if (!enabled || !websiteToken || !baseUrl) return;

    // Avoid double-injecting
    if (document.getElementById("chatwoot-sdk")) return;

    // Provide multiple hints for dark theme across different SDK versions
    // These keys are harmless if unrecognised
    window.chatwootSettings = {
      // commonly observed keys across releases
      darkMode: "dark",
      dark_mode: "dark",
      theme: "dark",
      colorScheme: "dark",
    };

    const s = document.createElement("script");
    s.id = "chatwoot-sdk";
    s.async = true;
    s.src = `${baseUrl}/packs/js/sdk.js`;
    s.onload = () => {
      // Try passing a dark mode hint if supported by the SDK
      try {
        window.chatwootSDK?.run({ websiteToken, baseUrl, darkMode: "dark" });
      } catch {
        window.chatwootSDK?.run({ websiteToken, baseUrl });
      }

      // After SDK is ready, attempt to force dark theme consistently
      ensureChatwootDark();

      // Retry a bit longer to catch re-renders/late mounts
      let attempts = 0;
      const id = window.setInterval(() => {
        attempts += 1;
        ensureChatwootDark();
        if (attempts > 20) window.clearInterval(id);
      }, 400);

      // A couple of delayed passes for good measure
      window.setTimeout(ensureChatwootDark, 3000);
      window.setTimeout(ensureChatwootDark, 8000);
    };
    document.body.appendChild(s);

    return () => {
      // Best-effort cleanup (Chatwoot doesn't expose a full teardown)
    };
  }, [enabled, websiteToken, baseUrl]);

  return null;
}

export function openChat() {
  if (typeof window !== "undefined") {
    // Pre-emptively enforce dark, then toggle, then re-enforce after mount
    ensureChatwootDark();
    window.$chatwoot?.toggle?.();
    window.chatwoot?.toggle?.();
    window.setTimeout(ensureChatwootDark, 50);
    window.setTimeout(ensureChatwootDark, 500);
  }
}
