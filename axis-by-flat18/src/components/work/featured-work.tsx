"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import Image from "next/image";
import Link from "next/link";

type WorkItem = {
  title: string;
  description: string;
  image: string;
  imageAlt: string;
  isDemo?: boolean;
  whatWeBuilt: string[];
  highlights: string[];
  stack: string[];
  link?: string;
};

const workItems: WorkItem[] = [
  {
    title: "Axis Demo: SaaS Billing UI",
    description:
      "A compact subscription flow with plans, invoices, and Stripe-ready screens.",
    image: "/placeholders/img/mockup-1.png",
    imageAlt: "Preview of the Axis SaaS billing demo interface",
    isDemo: true,
    whatWeBuilt: [
      "Plan comparison and upgrade flow",
      "Invoice history with downloadable receipts",
      "Billing settings panel with usage overview",
    ],
    highlights: [
      "Responsive layout ready for handoff",
      "Structured content and state hints for developers",
      "Accessible colour contrast right out of the box",
    ],
    stack: ["Next.js", "TypeScript", "Stripe Elements (mock data)"]
  },
  {
    title: "Axis Demo: Investor Dashboard",
    description:
      "A clean dashboard for KPIs, cohorts, and updates, designed for boardroom demos.",
    image: "/placeholders/img/mockup-2.png",
    imageAlt: "Preview of the Axis investor dashboard demo",
    isDemo: true,
    whatWeBuilt: [
      "Portfolio overview with segments",
      "Update feed and stakeholder notes",
      "Light and dark theme ready to wire to real data",
    ],
    highlights: [
      "Data cards sized for quick scanning",
      "Table patterns that work on mobile",
      "Annotation layer for investor commentary",
    ],
    stack: ["Next.js", "TypeScript", "Tailwind CSS"]
  },
  {
    title: "Selected Work by Flat 18",
    description: "A real project from our parent team. No claims, just the craft.",
    image: "/placeholders/img/mockup-3.png",
    imageAlt: "Selected Flat 18 project screenshot",
    whatWeBuilt: [
      "Marketing site with structured content model",
      "Design system tokens and component library",
      "Deployment pipeline and CMS handover",
    ],
    highlights: [
      "Collaborated with internal stakeholders from brief to launch",
      "Documented components for the in-house team",
      "Launch playbook and monitoring set-up",
    ],
    stack: ["Next.js", "Sanity", "Vercel"],
    link: "https://flat18.co.uk/",
  },
];

export function FeaturedWork() {
  return (
    <section
      id="case-studies"
      className="bg-[var(--bg-900)] py-20"
      style={{ contentVisibility: "auto", containIntrinsicSize: "1px 800px" }}
    >
      <div className="mx-auto max-w-[1200px] px-4">
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 16 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          <h2 className="font-display text-3xl font-semibold text-[var(--text-100)] sm:text-4xl">
            Work that shows how we build
          </h2>
          <p className="mt-3 text-base leading-7 text-[var(--text-300)]">
            Reference demos and Flat 18 work that demonstrate our approach to product, polish, and handover.
          </p>
        </motion.div>

        <div className="mt-12 grid gap-8 lg:grid-cols-3">
          {workItems.map((item, index) => (
            <motion.div
              key={item.title}
              initial={{ opacity: 0, y: 24 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.05 }}
              viewport={{ once: true }}
            >
              <Card className="h-full overflow-hidden rounded-[var(--radius-lg)] border border-[var(--border-600)] bg-[var(--surface-700)] shadow-[var(--elev-1)]">
                <div className="relative h-52 w-full overflow-hidden">
                  {item.isDemo ? (
                    <span className="absolute left-4 top-4 inline-flex items-center rounded-full bg-[var(--axis-purple)]/20 px-3 py-1 text-xs font-semibold uppercase tracking-widest text-[var(--axis-purple)]">
                      Demo
                    </span>
                  ) : null}
                  <Image
                    src={item.image}
                    alt={item.imageAlt}
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 33vw, 33vw"
                  />
                </div>
                <CardContent className="flex h-full flex-col gap-6 p-6">
                  <div>
                    <h3 className="font-display text-xl font-semibold text-[var(--text-100)]">
                      {item.title}
                    </h3>
                    <p className="mt-2 text-sm leading-6 text-[var(--text-300)]">
                      {item.description}
                    </p>
                  </div>

                  <div className="space-y-5 text-sm leading-6">
                    <div>
                      <p className="text-[var(--text-100)]">What we built</p>
                      <ul className="mt-2 space-y-2 text-[var(--text-300)]">
                        {item.whatWeBuilt.map((line) => (
                          <li key={line} className="flex gap-2">
                            <span className="mt-1 inline-block h-1.5 w-1.5 rounded-full bg-[var(--axis-cobalt)]" aria-hidden="true" />
                            {line}
                          </li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <p className="text-[var(--text-100)]">Highlights</p>
                      <ul className="mt-2 space-y-2 text-[var(--text-300)]">
                        {item.highlights.map((line) => (
                          <li key={line} className="flex gap-2">
                            <span className="mt-1 inline-block h-1.5 w-1.5 rounded-full bg-[var(--axis-secondary)]" aria-hidden="true" />
                            {line}
                          </li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <p className="text-[var(--text-100)]">Stack</p>
                      <div className="mt-2 flex flex-wrap gap-2 text-[var(--text-300)]">
                        {item.stack.map((line) => (
                          <span
                            key={line}
                            className="rounded-full bg-[var(--bg-800)] px-3 py-1 text-xs uppercase tracking-widest text-[var(--text-500)]"
                          >
                            {line}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>

                  {item.link ? (
                    <div className="mt-auto">
                      <Link
                        href={item.link}
                        className="inline-flex items-center gap-2 text-sm font-semibold text-[var(--axis-cobalt)] underline-offset-4 hover:underline"
                      >
                        View on Flat 18
                        <span aria-hidden="true" className="bi bi-arrow-up-right" />
                      </Link>
                    </div>
                  ) : null}
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
