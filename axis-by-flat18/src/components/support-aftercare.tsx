"use client";

import Link from "next/link";
import { motion } from "framer-motion";

import { Button } from "@/components/ui/button";

const included = [
  "Stability window for fixes immediately after launch",
  "Runbooks, environment variables, and owner handover",
  "Monitoring hooks configured before we go live",
];

const ongoing = [
  "Light monthly support for incremental improvements",
  "Rapid triage on production incidents",
  "Simple path back into the wider Flat 18 team when needed",
];

export function SupportAftercare() {
  return (
    <section
      id="support"
      className="bg-[var(--bg-800)] py-20"
      style={{ contentVisibility: "auto", containIntrinsicSize: "1px 720px" }}
    >
      <div className="mx-auto max-w-[1200px] px-4">
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 16 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          <h2 className="font-display text-3xl font-semibold text-[var(--text-100)] sm:text-4xl">
            After launch, we don’t disappear
          </h2>
          <p className="mt-3 mx-auto max-w-[680px] text-base leading-7 text-[var(--text-300)]">
            Every project includes a short post-launch window for stability and small fixes. If you need continued iterations or feature work, we offer a simple monthly support option.
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 24 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          viewport={{ once: true }}
          className="mt-12 grid gap-8 lg:grid-cols-2"
        >
          <div className="rounded-[var(--radius-lg)] border border-[var(--border-600)] bg-[var(--surface-700)] p-8 text-[var(--text-300)] shadow-[var(--elev-1)]">
            <p className="text-sm uppercase tracking-[0.3em] text-[var(--axis-secondary)]">
              What’s included post-launch
            </p>
            <ul className="mt-4 space-y-3 text-base leading-7">
              {included.map((item) => (
                <li key={item} className="flex gap-3">
                  <span className="mt-2 inline-block h-1.5 w-1.5 rounded-full bg-[var(--axis-cobalt)]" aria-hidden />
                  {item}
                </li>
              ))}
            </ul>
          </div>

          <div className="rounded-[var(--radius-lg)] border border-[var(--border-600)] bg-[var(--surface-700)] p-8 text-[var(--text-300)] shadow-[var(--elev-1)]">
            <p className="text-sm uppercase tracking-[0.3em] text-[var(--axis-secondary)]">
              Optional ongoing support
            </p>
            <ul className="mt-4 space-y-3 text-base leading-7">
              {ongoing.map((item) => (
                <li key={item} className="flex gap-3">
                  <span className="mt-2 inline-block h-1.5 w-1.5 rounded-full bg-[var(--axis-cobalt)]" aria-hidden />
                  {item}
                </li>
              ))}
            </ul>
            <Button
              asChild
              className="mt-6 inline-flex bg-[var(--axis-cobalt)] text-[var(--bg-950)] hover:bg-[var(--axis-cobalt)]/90 focus-visible:ring-[var(--axis-secondary)]"
            >
              <Link href="/support">Explore support options</Link>
            </Button>
          </div>
        </motion.div>
      </div>
    </section>
  );
}

