"use client";

import { motion } from "framer-motion";

export function WhyAxis() {
  return (
    <section
      id="problem"
      className="bg-[var(--bg-900)] py-20"
      style={{ contentVisibility: "auto", containIntrinsicSize: "1px 800px" }}
    >
      <div className="mx-auto grid max-w-[1200px] gap-12 px-4 lg:grid-cols-2">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true, amount: 0.5 }}
        >
          <h2 className="font-display text-3xl font-semibold text-[var(--text-100)] sm:text-4xl">
            Building your first product shouldn’t drain time or trust.
          </h2>
          <ul className="mt-6 space-y-4 text-base leading-7 text-[var(--text-300)]">
            <li className="flex gap-3">
              <span className="mt-1 inline-block h-2 w-2 rounded-full bg-[var(--axis-cobalt)]" aria-hidden="true" />
              Slipping timelines and unclear scope
            </li>
            <li className="flex gap-3">
              <span className="mt-1 inline-block h-2 w-2 rounded-full bg-[var(--axis-cobalt)]" aria-hidden="true" />
              Design that looks unfinished
            </li>
            <li className="flex gap-3">
              <span className="mt-1 inline-block h-2 w-2 rounded-full bg-[var(--axis-cobalt)]" aria-hidden="true" />
              Code you can’t confidently ship or hand over
            </li>
          </ul>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          viewport={{ once: true, amount: 0.5 }}
          className="rounded-[var(--radius-lg)] bg-[var(--surface-700)] p-8 text-[var(--text-100)] shadow-[var(--elev-1)]"
        >
          <p className="text-sm uppercase tracking-[0.3em] text-[var(--axis-secondary)]">
            How we work
          </p>
          <div className="mt-4 space-y-3 text-base leading-7 text-[var(--text-300)]">
            <p>— Fixed scope and transparent pricing</p>
            <p>— Wireframes in days, weekly demos thereafter</p>
            <p>— Production-grade code, clean handover, monitoring at launch</p>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
