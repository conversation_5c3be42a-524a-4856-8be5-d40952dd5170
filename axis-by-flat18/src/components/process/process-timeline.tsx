"use client";

import { motion } from "framer-motion";

const steps = [
  {
    title: "Kickoff",
    icon: "clipboard-check",
    outcome: "Align scope and priorities within 48 hours.",
    commitment: "Commitment: Summary plan and timeline shared before work begins.",
  },
  {
    title: "Design",
    icon: "palette",
    outcome: "Clickable wireframes and key screens in days.",
    commitment: "Commitment: Feedback session booked as soon as the prototype is ready.",
  },
  {
    title: "Build",
    icon: "cpu",
    outcome: "Iterative shipping with weekly demos.",
    commitment: "Commitment: Reviewed code, version control, and documented changelog.",
  },
  {
    title: "Launch",
    icon: "rocket-takeoff",
    outcome: "Deploy, monitor, and hand over cleanly.",
    commitment: "Commitment: Launch checklist, monitoring hooks, and handover pack delivered.",
  },
];

export function ProcessTimeline() {
  return (
    <section
      id="process"
      className="bg-[var(--bg-900)] py-20"
      style={{ contentVisibility: "auto", containIntrinsicSize: "1px 800px" }}
    >
      <div className="mx-auto max-w-[1200px] px-4">
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 16 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          <h2 className="font-display text-3xl font-semibold text-[var(--text-100)] sm:text-4xl">
            A four-step path to live
          </h2>
          <p className="mt-3 text-base leading-7 text-[var(--text-300)]">
            The same rhythm we follow on every engagement—clear, paced, and accountable.
          </p>
        </motion.div>

        <div className="mt-12 grid gap-10 lg:grid-cols-4">
          {steps.map((step, index) => (
            <motion.div
              key={step.title}
              initial={{ opacity: 0, y: 24 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.05 }}
              viewport={{ once: true }}
              className="flex flex-col rounded-[var(--radius-lg)] bg-[var(--surface-700)] p-6 shadow-[var(--elev-1)]"
            >
              <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-[var(--axis-cobalt)]/15 text-[var(--axis-cobalt)]">
                <i className={`bi bi-${step.icon} text-2xl`} aria-hidden="true" />
              </div>
              <h3 className="font-display text-xl font-semibold text-[var(--text-100)]">
                {step.title}
              </h3>
              <p className="mt-3 text-sm leading-6 text-[var(--text-300)]">
                {step.outcome}
              </p>
              <p className="mt-3 text-sm leading-6 text-[var(--text-500)]">
                {step.commitment}
              </p>
            </motion.div>
          ))}
        </div>

        <motion.p
          initial={{ opacity: 0, y: 16 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          viewport={{ once: true }}
          className="mt-12 text-center text-sm font-medium uppercase tracking-[0.3em] text-[var(--text-500)]"
        >
          We demo progress every week. No surprises.
        </motion.p>
      </div>
    </section>
  );
}
