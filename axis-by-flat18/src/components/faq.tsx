"use client";

import { motion } from "framer-motion";

const faqs = [
  {
    question: "Do you build from templates?",
    answer: "No. We design the UI for your use-case and use proven components where it helps speed and reliability.",
  },
  {
    question: "Who owns the code?",
    answer: "You do. We hand over repos and credentials at launch.",
  },
  {
    question: "Can you work with my existing backend?",
    answer: "Yes, if it’s documented and stable. We’ll assess during kickoff.",
  },
  {
    question: "How long does a typical project take?",
    answer: "Weeks, not months. We scope with you and keep to an agreed plan.",
  },
  {
    question: "What tech stack do you use?",
    answer: "Modern, widely-supported tools (e.g. React/Next.js, TypeScript, Postgres). We select pragmatically.",
  },
  {
    question: "Can you show references?",
    answer: "Yes. We’re new as Axis, so we share what’s appropriate from the team’s prior work on request.",
  },
];

export function FAQ() {
  return (
    <section
      id="faq"
      className="bg-[var(--bg-800)] py-20"
      style={{ contentVisibility: "auto", containIntrinsicSize: "1px 800px" }}
    >
      <div className="mx-auto max-w-[1200px] px-4">
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 16 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          <h2 className="font-display text-3xl font-semibold text-[var(--text-100)] sm:text-4xl">
            Still have questions?
          </h2>
          <p className="mt-3 text-base leading-7 text-[var(--text-300)] max-w-[680px] mx-auto">
            Quick answers to the common objections we hear from founders before they commit.
          </p>
        </motion.div>

        <div className="mt-10 overflow-hidden rounded-[var(--radius-lg)] border border-[var(--border-600)] bg-[var(--surface-700)]">
          {faqs.map((item, index) => (
            <motion.details
              key={item.question}
              className="group border-b border-[var(--border-600)] last:border-b-0"
              initial={{ opacity: 0, y: 8 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: index * 0.05 }}
              viewport={{ once: true }}
            >
              <summary className="flex cursor-pointer items-center justify-between gap-4 px-6 py-5 text-left text-base font-semibold text-[var(--text-100)] focus:outline-none focus-visible:ring-2 focus-visible:ring-[var(--axis-secondary)]">
                {item.question}
                <span className="bi bi-chevron-down text-[var(--text-500)] transition-transform duration-200 group-open:rotate-180" aria-hidden="true" />
              </summary>
              <p className="px-6 pb-6 text-sm leading-6 text-[var(--text-300)]">
                {item.answer}
              </p>
            </motion.details>
          ))}
        </div>
      </div>
    </section>
  );
}
