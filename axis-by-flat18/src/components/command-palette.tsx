"use client";

import { useEffect, useMemo, useState } from "react";
import { useRouter } from "next/navigation";
import { Search } from "lucide-react";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface CommandItem {
  title: string;
  description?: string;
  href: string;
  category: "Pages" | "Services" | "Pricing" | "Support" | "Learn";
}

const COMMANDS: CommandItem[] = [
  { title: "Home", href: "/", category: "Pages", description: "Return to the homepage" },
  { title: "Services", href: "/services", category: "Pages", description: "Packages and scope" },
  { title: "Process", href: "/process", category: "Pages", description: "Four-step delivery path" },
  { title: "Work", href: "/work", category: "Pages", description: "Library of demos and case studies" },
  { title: "Pricing", href: "/pricing", category: "Pages", description: "Tier comparison and FAQs" },
  { title: "Learn", href: "/learn", category: "Pages", description: "FAQ, playbooks, stack" },
  { title: "Start a project", href: "/start", category: "Services", description: "Send details or book time" },
  { title: "Book a call", href: "/call", category: "Services", description: "15-minute intro call" },
  { title: "Axis Start", href: "/services#start", category: "Pricing", description: "Proof-of-concept tier" },
  { title: "Axis Build", href: "/services#build", category: "Pricing", description: "Flagship MVP tier" },
  { title: "Axis Pro", href: "/services#pro", category: "Pricing", description: "Complex stakeholder demos" },
  { title: "Scale Support", href: "/services#scale", category: "Support", description: "Ongoing aftercare" },
  { title: "Support & Aftercare", href: "/support", category: "Support", description: "How we stabilise and assist" },
  { title: "FAQ", href: "/learn#faq", category: "Learn", description: "Common questions" },
];

const categories: CommandItem["category"][] = [
  "Pages",
  "Services",
  "Pricing",
  "Support",
  "Learn",
];

export function CommandPalette() {
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const [query, setQuery] = useState("");

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.metaKey || event.ctrlKey) && event.key.toLowerCase() === "k") {
        event.preventDefault();
        setOpen(true);
      }
      if (event.key === "Escape") {
        setOpen(false);
      }
    };

    const handleOpen = () => setOpen(true);

    window.addEventListener("keydown", handleKeyDown);
    window.addEventListener("axis:command-palette-open", handleOpen);

    return () => {
      window.removeEventListener("keydown", handleKeyDown);
      window.removeEventListener("axis:command-palette-open", handleOpen);
    };
  }, []);

  useEffect(() => {
    if (!open) {
      setQuery("");
    }
  }, [open]);

  const filtered = useMemo(() => {
    if (!query.trim()) return COMMANDS;
    const term = query.toLowerCase();
    return COMMANDS.filter((item) =>
      [item.title, item.description ?? "", item.category]
        .join(" ")
        .toLowerCase()
        .includes(term)
    );
  }, [query]);

  const handleSelect = (href: string) => {
    setOpen(false);
    router.push(href);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="max-w-xl gap-0 overflow-hidden border border-slate-200 bg-white p-0 shadow-xl shadow-slate-900/10">
        <DialogHeader className="sr-only">
          <DialogTitle>Quick find</DialogTitle>
          <DialogDescription>Search pages, services, and support docs</DialogDescription>
        </DialogHeader>
        <div className="flex items-center gap-3 border-b border-slate-200 px-4 py-3">
          <Search className="h-4 w-4 text-slate-400" aria-hidden />
          <input
            autoFocus
            value={query}
            onChange={(event) => setQuery(event.target.value)}
            placeholder="Search pages, services, or FAQs…"
            onKeyDown={(event) => {
              if (event.key === "Enter") {
                event.preventDefault();
                const first = filtered[0];
                if (first) {
                  handleSelect(first.href);
                }
              }
            }}
            className="h-10 w-full border-0 bg-transparent text-sm text-slate-900 outline-none placeholder:text-slate-400"
          />
          <span className="hidden text-xs font-medium text-slate-400 sm:inline-flex">Esc</span>
        </div>
        <div className="max-h-[320px] overflow-y-auto px-1 py-3">
          {categories.map((category) => {
            const items = filtered.filter((item) => item.category === category);
            if (!items.length) return null;
            return (
              <div key={category} className="px-3 py-2">
                <p className="text-xs font-semibold uppercase tracking-[0.3em] text-slate-400">
                  {category}
                </p>
                <ul className="mt-2 space-y-1">
                  {items.map((item) => (
                    <li key={item.title}>
                      <button
                        type="button"
                        className="flex w-full flex-col gap-1 rounded-xl px-3 py-3 text-left transition hover:bg-slate-100"
                        onClick={() => handleSelect(item.href)}
                      >
                        <span className="text-sm font-semibold text-slate-900">{item.title}</span>
                        {item.description ? (
                          <span className="text-xs text-slate-500">{item.description}</span>
                        ) : null}
                        <span className="text-[10px] uppercase tracking-[0.3em] text-slate-400">{item.href}</span>
                      </button>
                    </li>
                  ))}
                </ul>
              </div>
            );
          })}
          {!filtered.length ? (
            <div className="px-4 py-10 text-center text-sm text-slate-500">No results. Try a different keyword.</div>
          ) : null}
        </div>
        <div className="flex items-center justify-between border-t border-slate-200 px-4 py-3 text-xs text-slate-400">
          <span>Type to filter • Enter to open • Esc to close</span>
          <span>⌘K</span>
        </div>
      </DialogContent>
    </Dialog>
  );
}
