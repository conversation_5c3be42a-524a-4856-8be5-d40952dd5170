"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import <PERSON><PERSON> from "lottie-react";
import Link from "next/link";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { trackCtaClick, trackUmamiOnly } from "@/lib/twitter";
import { cn } from "@/lib/utils";
import pricingStartAnimation from "@/app/lottie/pricing-icons/1.json";
import pricingBuildAnimation from "@/app/lottie/pricing-icons/2.json";
import pricingProAnimation from "@/app/lottie/pricing-icons/3.json";

const tiers = [
  {
    name: "Start",
    price: "£1,495",
    animation: pricingStartAnimation,
    dimensions: "h-[60px] w-[60px]",
    headline: "For simple proof-of-concepts.",
    points: [
      "Core user flows and 3–5 key screens",
      "Basic auth and one data source",
      "Deployed to your cloud",
      "Handover and launch notes",
    ],
    accent: "var(--axis-cobalt)",
    highlight: false,
  },
  {
    name: "Build",
    price: "£2,995",
    animation: pricingBuildAnimation,
    dimensions: "h-[80px] w-[80px]",
    headline: "For MVPs targeting early users.",
    points: [
      "Designed key journeys and responsive UI",
      "Auth, database, and 1–3 integrations",
      "Production build with monitoring",
      "Weekly demos and clean handover",
    ],
    accent: "var(--axis-secondary)",
    highlight: true,
  },
  {
    name: "Pro",
    price: "£5,495",
    animation: pricingProAnimation,
    dimensions: "h-3190px] w-[390px]",
    headline: "For richer MVPs or stakeholder demos.",
    points: [
      "Expanded feature set and admin",
      "3–6 integrations or APIs",
      "Hardening, QA support",
      "Launch support and handover",
    ],
    accent: "var(--axis-purple)",
    highlight: false,
  },
] as const;

type TierName = (typeof tiers)[number]["name"];

export function PricingCards() {
  const [email, setEmail] = useState("");
  const [name, setName] = useState("");
  const [activeTier, setActiveTier] = useState<TierName | null>(null);
  const [loading, setLoading] = useState(false);

  const subscribe = async (tierName: TierName) => {
    if (!email) {
      alert("Please enter your email");
      return;
    }
    setLoading(true);
    try {
      const utm: Record<string, string> = {};
      ["utm_source", "utm_medium", "utm_campaign", "utm_content", "utm_term"].forEach((key) => {
        const value = sessionStorage.getItem(key);
        if (value) utm[key] = value;
      });
      const res = await fetch("/api/invoiceninja/subscribe", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          name,
          email,
          tier: tierName.toLowerCase(),
          mode: "subscription",
          utm,
        }),
      });
      const data = await res.json();
      if (res.ok && data?.redirectUrl) {
        window.location.href = data.redirectUrl as string;
      } else {
        alert(data?.error || "Unable to start the project");
      }
    } catch {
      alert("Network error");
    } finally {
      setLoading(false);
    }
  };

  return (
    <section
      id="pricing"
      className="bg-[var(--bg-800)] py-20"
      style={{ contentVisibility: "auto", containIntrinsicSize: "1px 960px" }}
    >
      <div className="mx-auto max-w-[1200px] px-4">
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 16 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          <h2 className="font-display text-3xl font-semibold text-[var(--text-100)] sm:text-4xl">
            Fixed‑price packages that match typical first builds
          </h2>
          <p className="mt-3 mx-auto max-w-[680px] text-base leading-7 text-[var(--text-300)]">
            Pick the tier that mirrors your launch goals. No vanity metrics, just scope clarity and the same senior team each time.
          </p>
        </motion.div>

        <div className="mt-12 grid gap-8 lg:grid-cols-3">
          {tiers.map((tier, index) => (
            <motion.div
              key={tier.name}
              initial={{ opacity: 0, y: 24 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.05 }}
              viewport={{ once: true }}
            >
              <Card
                className={cn(
                  "relative h-full overflow-hidden rounded-[var(--radius-lg)] border border-[var(--border-600)] bg-[var(--surface-700)] text-[var(--text-100)] shadow-[var(--elev-1)] transition",
                  tier.highlight && "border-[var(--axis-secondary)]/60 shadow-[var(--elev-2)]"
                )}
              >
                {tier.highlight ? (
                  <span className="absolute right-6 top-6 inline-flex items-center rounded-full bg-[var(--axis-secondary)]/20 px-3 py-1 text-xs font-semibold uppercase tracking-[0.28em] text-[var(--axis-secondary)]">
                    Most picked
                  </span>
                ) : null}
                <CardHeader className="space-y-4 pb-0">
                  <div className="flex items-center justify-between gap-4">
                    <div>
                      <CardTitle className="text-3xl font-semibold">
                        {tier.price}
                      </CardTitle>
                      <CardDescription className="mt-1 text-sm text-[var(--text-300)]">
                        {tier.headline}
                      </CardDescription>
                    </div>
                    <Lottie
                      animationData={tier.animation}
                      className={tier.dimensions}
                      loop
                      role="img"
                      aria-label={`${tier.name} tier animation`}
                    />
                  </div>
                </CardHeader>
                <CardContent className="pt-6">
                  <ul className="space-y-3 text-sm leading-6 text-[var(--text-300)]">
                    {tier.points.map((point) => (
                      <li key={point} className="flex items-start gap-3">
                        <span
                          className="mt-2 inline-flex h-1.5 w-1.5 rounded-full"
                          style={{ backgroundColor: tier.accent }}
                          aria-hidden
                        />
                        <span>{point}</span>
                      </li>
                    ))}
                  </ul>
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button
                        className="mt-8 w-full justify-center bg-[var(--axis-cobalt)] text-[var(--bg-950)] hover:bg-[var(--axis-cobalt)]/90 focus-visible:ring-[var(--axis-secondary)]"
                        onClick={() => {
                          setActiveTier(tier.name);
                          setEmail("");
                          setName("");
                          trackCtaClick({
                            label: "cta_start_project_clicked",
                            location: `pricing-${tier.name.toLowerCase()}`,
                            href: "/start",
                          });
                          if (tier.name === "Build") {
                            trackUmamiOnly("pricing_build_selected", { tier: tier.name });
                          }
                        }}
                      >
                        Start a project
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-md rounded-[var(--radius-lg)] border border-[var(--border-600)] bg-[var(--surface-700)] text-[var(--text-100)] shadow-[var(--elev-2)]">
                      <DialogHeader>
                        <DialogTitle>Start Axis {tier.name}</DialogTitle>
                      </DialogHeader>
                      <div className="grid gap-3">
                        <Input
                          placeholder="Email"
                          type="email"
                          value={email}
                          onChange={(event) => setEmail(event.target.value)}
                        />
                        <Input
                          placeholder="Name (optional)"
                          value={name}
                          onChange={(event) => setName(event.target.value)}
                        />
                        <Button
                          onClick={() => {
                            const targetTier = activeTier ?? tier.name;
                            if (targetTier === "Build") {
                              trackUmamiOnly("pricing_build_selected", {
                                tier: targetTier,
                                context: "dialog",
                              });
                            }
                            trackCtaClick({
                              label: "cta_start_project_clicked",
                              location: `pricing-${tier.name.toLowerCase()}-dialog`,
                              href: "/start",
                            });
                            subscribe(targetTier);
                          }}
                          disabled={loading}
                          className="justify-center bg-[var(--axis-cobalt)] text-[var(--bg-950)] hover:bg-[var(--axis-cobalt)]/90 focus-visible:ring-[var(--axis-secondary)]"
                        >
                          {loading ? "Redirecting…" : "Start a project"}
                        </Button>
                        <div className="space-y-2 rounded-[var(--radius-sm)] border border-[var(--border-600)] bg-[var(--bg-900)] p-4 text-xs text-[var(--text-300)]">
                          <p className="font-semibold text-[var(--text-100)]">Existing customer?</p>
                          <Button
                            variant="outline"
                            onClick={async () => {
                              if (!email) {
                                alert("Enter your email");
                                return;
                              }
                              setLoading(true);
                              try {
                                const res = await fetch("/api/invoiceninja/portal-invite", {
                                  method: "POST",
                                  headers: { "Content-Type": "application/json" },
                                  body: JSON.stringify({ email, name }),
                                });
                                const data = await res.json();
                                if (res.ok && data?.redirectUrl) {
                                  window.location.href = data.redirectUrl as string;
                                } else {
                                  alert(data?.error || "Unable to create login link");
                                }
                              } finally {
                                setLoading(false);
                              }
                            }}
                            className="w-full justify-center"
                          >
                            Magic login
                          </Button>
                          <p>We’ll open a secure portal link to authenticate you automatically.</p>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        <div className="mt-12 space-y-3 text-center text-sm text-[var(--text-500)]">
          <p>We don’t publish vanity metrics. If you need references, ask. We’re happy to share what we can.</p>
          <p className="text-[var(--text-300)]">Fixed scope, clear timeline, staged payments.</p>
        </div>

        <div className="mt-10 flex flex-col items-center justify-center gap-3 sm:flex-row">
          <Button
            asChild
            size="lg"
            className="bg-[var(--axis-cobalt)] text-[var(--bg-950)] hover:bg-[var(--axis-cobalt)]/90 focus-visible:ring-[var(--axis-secondary)]"
          >
            <Link href="/start">Start a project</Link>
          </Button>
          <Button
            asChild
            variant="ghost"
            size="lg"
            className="border border-[var(--axis-cobalt)]/60 bg-transparent text-[var(--text-100)] hover:bg-[var(--surface-700)]"
          >
            <Link href="/call">Book a 15-minute call</Link>
          </Button>
        </div>
      </div>
    </section>
  );
}
