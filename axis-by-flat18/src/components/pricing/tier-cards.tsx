"use client";

import { motion } from "framer-motion";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import Image from "next/image";
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";

const tiers = [
  {
    name: "Start",
    price: "£1,495",
    duration: "1 week",
    icon: "/placeholders/img/tier-start.svg",
    description: "Landing page, microsite, or single-feature MVP",
    features: [
      "1 week delivery",
      "1 revision included",
      "Responsive design",
      "Basic analytics setup",
      "Production deployment",
    ],
    accent: "axis-accent-1",
  },
  {
    name: "Build",
    price: "£2,995",
    duration: "2–3 weeks",
    icon: "/placeholders/img/tier-build.svg",
    description: "MVP app with integrations and authentication",
    features: [
      "2-3 week delivery",
      "Up to 2 integrations",
      "User authentication",
      "2 revisions included",
      "Database setup",
      "Production deployment",
    ],
    accent: "axis-accent-2",
    popular: true,
  },
  {
    name: "Pro",
    price: "£5,495",
    duration: "4–5 weeks",
    icon: "/placeholders/img/tier-pro.svg",
    description: "Complex apps and dashboards with multiple integrations",
    features: [
      "4-5 week delivery",
      "Up to 4 integrations",
      "Advanced dashboard",
      "2 revisions included",
      "Admin panel",
      "Advanced analytics",
      "Production deployment",
    ],
    accent: "axis-accent-3",
  },
];

export function TierCards() {
  const [email, setEmail] = useState("");
  const [name, setName] = useState("");
  const [activeTier, setActiveTier] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [sub, setSub] = useState<{ url: string } | null>(null);

  const subscribe = async (tier: string) => {
    if (!email) { alert("Please enter your email"); return; }
    setLoading(true);
    try {
      const utm: Record<string, string> = {};
      ["utm_source","utm_medium","utm_campaign","utm_content","utm_term"].forEach(k => { const v = sessionStorage.getItem(k); if (v) utm[k] = v; });
      const res = await fetch("/api/invoiceninja/subscribe", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ name, email, tier: tier.toLowerCase(), mode: "subscription", utm })
      });
      const data = await res.json();
      if (res.ok && data?.redirectUrl) {
        setSub({ url: data.redirectUrl as string });
        setTimeout(() => { try { window.location.href = data.redirectUrl as string; } catch {} }, 600);
      } else {
        alert(data?.error || "Unable to subscribe");
      }
    } catch { alert("Network error"); }
    finally { setLoading(false); }
  };

  return (
    <section className="py-24" id="pricing" style={{ contentVisibility: "auto", containIntrinsicSize: "1px 1000px" }}>
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="font-display text-4xl font-bold text-axis-text mb-4">
            Choose Your Build Tier
          </h2>
          <p className="text-xl text-[var(--text-300)] max-w-2xl mx-auto">
            Fixed scope. Fixed timeline. Fixed price. No surprises.
          </p>
        </div>

        <div className="grid gap-8 lg:grid-cols-3 max-w-6xl mx-auto">
          {tiers.map((tier, index) => (
            <motion.div
              key={tier.name}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className={`relative h-full border-2 ${
                tier.popular 
                  ? 'border-axis-accent-2 shadow-lg shadow-axis-accent-2/20' 
                  : 'border-border hover:border-axis-accent-1/50'
              } transition-all duration-300`}>
                {tier.popular && (
                  <div className="absolute -top-4 left-1/2 -translate-x-1/2">
                    <span className="bg-axis-accent-2 text-white px-4 py-1 rounded-full text-sm font-medium">
                      Most Popular
                    </span>
                  </div>
                )}
                
                <CardHeader className="text-center pb-6">
                  <div className="mb-4 flex justify-center">
                    <Image
                      src={tier.icon}
                      alt={`${tier.name} tier icon`}
                      width={64}
                      height={64}
                      className="h-16 w-16"
                    />
                  </div>
                  <CardTitle className="text-2xl font-display text-axis-text">
                    Axis {tier.name}
                  </CardTitle>
                  <div className="mt-2">
                    <span className={`text-4xl font-bold text-${tier.accent}`}>
                      {tier.price}
                    </span>
                    <span className="text-[var(--text-500)] ml-2">({tier.duration})</span>
                  </div>
                  <CardDescription className="text-[var(--text-300)] mt-2">
                    {tier.description}
                  </CardDescription>
                </CardHeader>

                <CardContent>
                  <ul className="space-y-3 mb-6 w-fit mx-auto text-left">
                    {tier.features.map((feature) => (
                      <li key={feature} className="flex items-center text-[var(--text-300)]">
                        <div className={`h-2 w-2 rounded-full bg-${tier.accent} mr-3 flex-shrink-0`} />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </CardContent>
                <div className="px-6 pb-6">
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button className={`w-full ${tier.popular ? 'bg-axis-accent-2 hover:bg-axis-accent-2/90' : ''}`} onClick={() => { setActiveTier(tier.name); setEmail(""); setName(""); setSub(null); }}>
                        Subscribe to Axis {tier.name}
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Subscribe to Axis {tier.name}</DialogTitle>
                      </DialogHeader>
                      <div className="grid gap-3">
                        <Input placeholder="Your email" type="email" value={email} onChange={(e)=>setEmail(e.target.value)} />
                        <Input placeholder="Your name (optional)" value={name} onChange={(e)=>setName(e.target.value)} />
                        <Button onClick={()=> subscribe(activeTier || tier.name)} disabled={loading}>
                          {loading ? "Redirecting…" : `Subscribe to ${tier.name}`}
                        </Button>
                        <div className="grid gap-2">
                          <div className="text-xs text-[var(--text-500)]">Existing customer?</div>
                          <Button variant="outline" onClick={async ()=>{
                            if (!email) { alert('Enter your email'); return; }
                            setLoading(true);
                            try {
                              const res = await fetch('/api/invoiceninja/portal-invite', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ email, name }) });
                              const data = await res.json();
                              if (res.ok && data?.redirectUrl) window.location.href = data.redirectUrl as string;
                              else alert(data?.error || 'Unable to create login link');
                            } finally { setLoading(false); }
                          }}>Magic Login</Button>
                        </div>
                        {sub && (
                          <div className="mt-2 p-3 rounded-md border border-axis-accent-2/30 bg-axis-accent-2/10">
                            <p className="text-sm text-axis-text">Redirecting you to secure checkout…</p>
                            <div className="mt-2 flex gap-2">
                              <Button size="sm" onClick={()=> window.location.href = sub.url}>Open Checkout</Button>
                              <Button size="sm" variant="outline" onClick={async ()=>{ await navigator.clipboard.writeText(sub.url); alert('Link copied'); }}>Copy Link</Button>
                            </div>
                            <p className="text-xs text-[var(--text-500)] mt-2">If it doesn&apos;t open automatically, use the buttons above.</p>
                          </div>
                        )}
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              </Card>
            </motion.div>
          ))}
        </div>

        <div className="text-center mt-16">
          <div className="bg-card border rounded-lg p-6 max-w-2xl mx-auto">
            <h3 className="font-display text-xl font-semibold text-axis-text mb-2">
              Axis Scale Support
            </h3>
            <p className="text-[var(--text-300)] mb-4">
              <span className="font-semibold text-axis-accent-1">£750/month</span> — 
              Minor improvements, fixes, and light features for any completed project
            </p>
            <p className="text-sm text-[var(--text-500)]">
              Add-on service available after project completion
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
