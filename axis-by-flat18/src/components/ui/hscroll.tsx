"use client";

import { useEffect, useRef, useState, type <PERSON>psWithChildren, type ReactNode } from "react";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";

interface HScrollProps {
  ariaLabel?: string;
  itemMinWidth?: number; // px
  className?: string;
}

export function HorizontalScroller({
  ariaLabel = "Horizontal scroller",
  itemMinWidth = 320,
  className,
  children,
}: PropsWithChildren<HScrollProps>) {
  const ref = useRef<HTMLDivElement | null>(null);
  const [atStart, setAtStart] = useState(true);
  const [atEnd, setAtEnd] = useState(false);
  const [showHint, setShowHint] = useState(true);

  function updateState(el: HTMLDivElement) {
    const { scrollLeft, scrollWidth, clientWidth } = el;
    setAtStart(scrollLeft <= 2);
    setAtEnd(scrollLeft + clientWidth >= scrollWidth - 2);
  }

  useEffect(() => {
    const el = ref.current;
    if (!el) return;
    updateState(el);
    const onScroll = () => {
      updateState(el);
      if (showHint && el.scrollLeft > 8) setShowHint(false);
    };
    const onResize = () => updateState(el);
    el.addEventListener("scroll", onScroll, { passive: true });
    window.addEventListener("resize", onResize);
    const hideTimer = window.setTimeout(() => setShowHint(false), 4000);
    return () => {
      el.removeEventListener("scroll", onScroll);
      window.removeEventListener("resize", onResize);
      window.clearTimeout(hideTimer);
    };
  }, [showHint]);

  function scrollBy(delta: number) {
    const el = ref.current;
    if (!el) return;
    el.scrollBy({ left: delta, behavior: "smooth" });
  }

  return (
    <div className={`relative ${className || ""}`}>
      {/* Edge gradients as scroll cues */}
      {!atStart && (
        <div className="pointer-events-none absolute inset-y-0 left-0 w-16 bg-gradient-to-r from-background to-transparent z-10" />
      )}
      {!atEnd && (
        <div className="pointer-events-none absolute inset-y-0 right-0 w-16 bg-gradient-to-l from-background to-transparent z-10" />
      )}

      {/* Scroll container */}
      <div
        ref={ref}
        role="region"
        aria-label={ariaLabel}
        tabIndex={0}
        className="no-scrollbar overflow-x-auto overflow-y-hidden scroll-smooth snap-x snap-mandatory"
      >
        <div className="flex gap-6 px-1 py-2" style={{ contain: "content" }}>
          {/* Ensure children have snap alignment and min width */}
          {(
            (Array.isArray(children) ? (children as ReactNode[]) : [children])
          ).map((child, i) => (
            <div key={i} className="snap-start shrink-0" style={{ minWidth: itemMinWidth }}>
              {child}
            </div>
          ))}
        </div>
      </div>

      {/* Clickable controls as fallback */}
      <div className="absolute inset-y-0 left-0 flex items-center pl-2 z-20">
        <Button
          variant="outline"
          size="icon"
          className="h-9 w-9 bg-background/80 backdrop-blur border-border"
          onClick={() => scrollBy(-1 * (ref.current?.clientWidth || 400) * 0.9)}
          aria-label="Scroll left"
          disabled={atStart}
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
      </div>
      <div className="absolute inset-y-0 right-0 flex items-center pr-2 z-20">
        <Button
          variant="outline"
          size="icon"
          className="h-9 w-9 bg-background/80 backdrop-blur border-border"
          onClick={() => scrollBy((ref.current?.clientWidth || 400) * 0.9)}
          aria-label="Scroll right"
          disabled={atEnd}
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>

      {/* Small hint chip for discoverability */}
      {showHint && !atEnd && (
        <div className="absolute bottom-3 right-12 z-20 select-none">
          <div className="px-3 py-1 rounded-full text-xs bg-foreground text-background/90 shadow">
            Scroll →
          </div>
        </div>
      )}
    </div>
  );
}
