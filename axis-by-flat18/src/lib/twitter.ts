// Reusable Twitter pixel helpers

export type TwitterContentItem = {
  content_type?: string | null;
  content_id?: string | null;
  content_name?: string | null;
  content_price?: number | null;
  num_items?: number | null;
  content_group_id?: string | null;
};

export type TwitterEventPayload = {
  value?: number | null;
  currency?: string | null;
  contents?: TwitterContentItem[];
  conversion_id?: string | null;
  email_address?: string | null;
  phone_number?: string | null; // E164
  [key: string]: unknown;
};

const DEFAULT_EVENT_ID = "tw-qfnk1-qfnk2";

function isProd() {
  return typeof process !== "undefined" && process.env.NODE_ENV === "production";
}

export function uuid(): string {
  try {
    const g = globalThis as unknown as { crypto?: { randomUUID?: () => string } };
    if (g.crypto?.randomUUID) return g.crypto.randomUUID();
  } catch {}
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0;
    const v = c === "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

type Twq = (action: string, eventId?: string, payload?: unknown) => void;

declare global {
  interface Window {
    twq?: Twq;
    umami?: { track: (event: string, data?: Record<string, unknown>) => void };
  }
}

function trackUmamiEvent(event: string, data?: Record<string, unknown>) {
  try {
    if (typeof window === "undefined") return;
    window.umami?.track(event, data);
  } catch (error) {
    console.warn("Umami event tracking failed", error);
  }
}

export function trackUmamiOnly(event: string, data?: Record<string, unknown>) {
  trackUmamiEvent(event, data);
}

function getTwq(): Twq | null {
  if (!isProd()) return null;
  if (typeof window === "undefined") return null;
  const twq = (window as Window).twq;
  return typeof twq === "function" ? twq : null;
}

export function trackTwitterEvent(payload: TwitterEventPayload, eventId: string = DEFAULT_EVENT_ID) {
  try {
    const twq = getTwq();
    if (!twq) return;
    twq("event", eventId, payload);
  } catch (e) {
    console.warn("Twitter event tracking failed", e);
  }
}

export function estimateValueFromBudget(budget: string): number | null {
  switch (budget) {
    case "start":
      return 2250;
    case "build":
      return 4500;
    case "pro":
      return 7500;
    case "scale":
      return null;
    default:
      return null;
  }
}

// Specific helpers
export function trackLeadSubmission(args: { email: string; budget: string; tier: string; phone?: string | null }) {
  const value = estimateValueFromBudget(args.budget);
  trackTwitterEvent({
    value: value ?? null,
    currency: value ? "GBP" : null,
    contents: [
      {
        content_type: "service",
        content_id: `tier-${args.tier}`,
        content_name: `Axis ${args.tier.charAt(0).toUpperCase()}${args.tier.slice(1)}`,
        content_price: value ?? null,
        num_items: 1,
        content_group_id: "axis-tier",
      },
    ],
    conversion_id: uuid(),
    email_address: args.email || null,
    phone_number: args.phone || null,
  });
}

export function trackCtaClick(args: { label: string; location?: string; href?: string; email?: string | null }) {
  const name = args.label;
  trackUmamiEvent(name, { location: args.location, href: args.href });
  trackTwitterEvent({
    contents: [
      {
        content_type: "cta",
        content_id: `${(args.location || "global").toLowerCase()}::${name.toLowerCase().replace(/\s+/g, "-")}`,
        content_name: name,
        num_items: 1,
        content_group_id: "cta",
      },
    ],
    conversion_id: uuid(),
    email_address: args.email || null,
  });
}

export function trackOnboardingStep(args: { step: string; tier?: string; email?: string | null }) {
  trackTwitterEvent({
    contents: [
      {
        content_type: "onboarding",
        content_id: `step-${args.step}`,
        content_name: args.step,
        num_items: 1,
        content_group_id: args.tier ? `tier-${args.tier}` : "onboarding",
      },
    ],
    conversion_id: uuid(),
    email_address: args.email || null,
  });
}
