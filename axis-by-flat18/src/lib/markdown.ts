import React from 'react';

interface HeadingInfo {
  id: string;
  text: string;
  level: number;
}

export function parseMarkdownContent(content: string): {
  html: React.ReactNode[];
  headings: HeadingInfo[];
} {
  const lines = content.split('\n');
  const elements: React.ReactNode[] = [];
  const headings: HeadingInfo[] = [];
  let currentIndex = 0;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // Skip empty lines
    if (!line.trim()) {
      continue;
    }

    // Parse headings
    if (line.startsWith('#')) {
      const level = line.match(/^#+/)?.[0].length || 1;
      const text = line.replace(/^#+\s*/, '');
      const id = generateId(text);
      
      headings.push({ id, text, level });
      
      type HeadingTag = 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
      const HeadingComponent = `h${Math.min(level, 6)}` as HeadingTag;
      elements.push(
        React.createElement(
          HeadingComponent,
          {
            key: `heading-${currentIndex++}`,
            id,
            className: getHeadingClassName(level),
          },
          text
        )
      );
      continue;
    }

    // Parse bold text date format
    if (line.startsWith('**Date:**')) {
      const dateText = line.replace('**Date:**', '').trim();
      elements.push(
        React.createElement(
          'p',
          {
            key: `date-${currentIndex++}`,
            className: 'text-sm text-[var(--text-300)] mb-6 font-medium',
          },
          `Last updated: ${dateText}`
        )
      );
      continue;
    }

    // Parse list items
    if (line.startsWith('-')) {
      const listItems = [line];
      let j = i + 1;
      
      // Collect consecutive list items
      while (j < lines.length && lines[j].startsWith('-')) {
        listItems.push(lines[j]);
        j++;
      }
      
      const listElements = listItems.map((item, idx) => {
        const text = item.replace(/^-\s*/, '');
        return React.createElement(
          'li',
          {
            key: `list-item-${currentIndex++}-${idx}`,
            className: 'mb-2',
          },
          parseInlineFormatting(text)
        );
      });
      
      elements.push(
        React.createElement(
          'ul',
          {
            key: `list-${currentIndex++}`,
            className: 'list-disc list-inside mb-6 ml-4 space-y-2',
          },
          ...listElements
        )
      );
      
      i = j - 1; // Skip processed lines
      continue;
    }

    // Parse regular paragraphs
    if (line.trim()) {
      elements.push(
        React.createElement(
          'p',
          {
            key: `paragraph-${currentIndex++}`,
            className: 'mb-4 leading-relaxed text-[var(--text-300)]',
          },
          parseInlineFormatting(line)
        )
      );
    }
  }

  return { html: elements, headings };
}

function parseInlineFormatting(text: string): React.ReactNode {
  // Handle bold text with **
  const boldRegex = /\*\*(.*?)\*\*/g;
  const parts = text.split(boldRegex);
  
  return parts.map((part, index) => {
    if (index % 2 === 1) {
      // This is bold text
      return React.createElement('strong', { key: index, className: 'font-semibold text-axis-text' }, part);
    }
    return part;
  });
}

function generateId(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/--+/g, '-')
    .trim();
}

function getHeadingClassName(level: number): string {
  const baseClasses = 'font-display font-bold text-axis-text mb-4 scroll-mt-24';
  
  switch (level) {
    case 1:
      return `${baseClasses} text-3xl lg:text-4xl mb-6`;
    case 2:
      return `${baseClasses} text-2xl lg:text-3xl mt-8 mb-6`;
    case 3:
      return `${baseClasses} text-xl lg:text-2xl mt-6`;
    case 4:
      return `${baseClasses} text-lg lg:text-xl mt-6`;
    default:
      return `${baseClasses} text-base lg:text-lg mt-4`;
  }
}

export function extractLastUpdated(content: string): string {
  const dateMatch = content.match(/\*\*Date:\*\*\s*(.+)/);
  return dateMatch ? dateMatch[1].trim() : 'Not specified';
}
