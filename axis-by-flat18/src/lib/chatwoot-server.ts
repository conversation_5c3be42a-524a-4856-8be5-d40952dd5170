export async function createChatwootConversation(email?: string, content?: string) {
  try {
    const base = process.env.CHATWOOT_API_BASE;
    const accountId = process.env.CHATWOOT_ACCOUNT_ID;
    const inboxId = process.env.CHATWOOT_INBOX_ID;
    const token = process.env.CHATWOOT_API_TOKEN;
    if (!base || !accountId || !inboxId || !token) return;

    // Create or find contact
    const contactRes = await fetch(`${base}/api/v1/accounts/${accountId}/contacts`, {
      method: "POST",
      headers: { "Content-Type": "application/json", Authorization: `Bearer ${token}` },
      body: JSON.stringify({ email, name: email })
    });
    const contact = await contactRes.json();
    const contactId = contact?.id || contact?.payload?.contact?.id;

    // Create conversation
    const convRes = await fetch(`${base}/api/v1/accounts/${accountId}/conversations`, {
      method: "POST",
      headers: { "Content-Type": "application/json", Authorization: `Bearer ${token}` },
      body: JSON.stringify({ source_id: `${Date.now()}-${email || 'unknown'}`, inbox_id: Number(inboxId), contact_id: contactId })
    });
    const conv = await convRes.json();
    const convId = conv?.id || conv?.payload?.conversation?.id;

    if (convId && content) {
      await fetch(`${base}/api/v1/accounts/${accountId}/conversations/${convId}/messages`, {
        method: "POST",
        headers: { "Content-Type": "application/json", Authorization: `Bearer ${token}` },
        body: JSON.stringify({ content, message_type: "incoming" })
      });
    }
  } catch {
    // ignore errors
  }
}
