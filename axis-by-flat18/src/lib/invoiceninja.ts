export type Tier = "start" | "build" | "pro";

function env(name: string) {
  return process.env[name];
}

export function getPublicSubscriptionUrl(tier: Tier): string | undefined {
  const map: Record<Tier, string | undefined> = {
    start: env("IN_SUB_START_URL"),
    build: env("IN_SUB_BUILD_URL"),
    pro: env("IN_SUB_PRO_URL"),
  };
  return map[tier];
}

export function getTierAmount(tier: Tier): number | undefined {
  const raw = {
    start: env("IN_TIER_START_AMOUNT"),
    build: env("IN_TIER_BUILD_AMOUNT"),
    pro: env("IN_TIER_PRO_AMOUNT"),
  }[tier];
  if (!raw) return undefined;
  const n = Number(raw);
  return Number.isFinite(n) ? n : undefined;
}

type INContact = { email?: string; name?: string };
type INClient = { id?: string; name?: string; contacts?: INContact[] };

export async function findOrCreateClient(host: string, apiToken: string, payload: { name?: string; email: string; utm?: Record<string, unknown> }) {
  const base = new URL("/api/v1", host).toString().replace(/\/$/, "");
  // Try find by email
  const search = await fetch(`${base}/clients?filter=${encodeURIComponent(payload.email)}`, {
    headers: { "X-Api-Token": apiToken },
    cache: "no-store",
  });
  if (search.ok) {
    const data = await search.json();
    const arr = (data?.data as INClient[] | undefined) || [];
    const match = arr.find((c) => (c?.contacts || []).some((ct) => (ct.email || '').toLowerCase() === payload.email.toLowerCase()));
    if (match) return match;
  }
  // Create client
  const notes = payload.utm ? `utm: ${JSON.stringify(payload.utm)}` : undefined;
  const create = await fetch(`${base}/clients`, {
    method: "POST",
    headers: { "X-Api-Token": apiToken, "Content-Type": "application/json" },
    body: JSON.stringify({
      name: payload.name || payload.email,
      contacts: [{ email: payload.email, name: payload.name || payload.email }],
      public_notes: notes,
    }),
  });
  if (!create.ok) throw new Error(`Failed to create client: ${create.status}`);
  const created = await create.json();
  return created?.data ?? created;
}

export async function createInvoiceInvitationForTier(host: string, apiToken: string, args: { clientId: string; tier: Tier; currency?: string; utm?: Record<string, unknown> }) {
  const base = new URL("/api/v1", host).toString().replace(/\/$/, "");
  const amount = getTierAmount(args.tier);
  if (!amount) throw new Error(`Missing amount for tier ${args.tier}`);
  const itemName = {
    start: "Axis Start",
    build: "Axis Build",
    pro: "Axis Pro",
  }[args.tier];
  const body: Record<string, unknown> = {
    client_id: args.clientId,
    currency_id: undefined, // let IN default if not provided
    line_items: [
      {
        cost: amount,
        product_key: itemName,
        notes: `Tier: ${itemName}`,
        quantity: 1,
      },
    ],
    public_notes: args.utm ? `utm: ${JSON.stringify(args.utm)}` : undefined,
  };
  const res = await fetch(`${base}/invoices`, {
    method: "POST",
    headers: { "X-Api-Token": apiToken, "Content-Type": "application/json" },
    body: JSON.stringify(body),
  });
  if (!res.ok) throw new Error(`Failed to create invoice: ${res.status}`);
  const data = await res.json();
  const invoice = data?.data ?? data;
  // In IN v5, invitations are usually created along with invoice; otherwise create via /invitations
  const invitation = invoice?.invitations?.[0];
  if (invitation?.link) return invitation.link as string;
  // Fallback: try creating invitation
  const invRes = await fetch(`${base}/invitations`, {
    method: "POST",
    headers: { "X-Api-Token": apiToken, "Content-Type": "application/json" },
    body: JSON.stringify({ invitations: [{ invoice_id: invoice?.id }] }),
  });
  const invData = await invRes.json();
  const link = invData?.data?.[0]?.link;
  if (link) return link as string;
  // Construct a reasonable client portal URL if API doesn’t return a link
  const key = invData?.data?.[0]?.key || invitation?.key;
  if (key) {
    return `${host.replace(/\/$/, "")}/client/invoice/${key}`;
  }
  throw new Error("No invitation link available");
}

export async function createQuoteInvitationForClient(host: string, apiToken: string, args: { clientId: string; notes?: string }) {
  const base = new URL("/api/v1", host).toString().replace(/\/$/, "");
  const body: Record<string, unknown> = {
    client_id: args.clientId,
    line_items: [
      {
        cost: 0,
        product_key: "Portal Login",
        notes: args.notes || "Login link",
        quantity: 1,
      },
    ],
  };
  const res = await fetch(`${base}/quotes`, {
    method: "POST",
    headers: { "X-Api-Token": apiToken, "Content-Type": "application/json" },
    body: JSON.stringify(body),
  });
  if (!res.ok) throw new Error(`Failed to create quote: ${res.status}`);
  const data = await res.json();
  const quote = data?.data ?? data;
  const invitation = quote?.invitations?.[0];
  if (invitation?.link) return invitation.link as string;
  // Fallback: create invitation explicitly
  const invRes = await fetch(`${base}/invitations`, {
    method: "POST",
    headers: { "X-Api-Token": apiToken, "Content-Type": "application/json" },
    body: JSON.stringify({ invitations: [{ quote_id: quote?.id }] }),
  });
  const invData = await invRes.json();
  const link = invData?.data?.[0]?.link;
  if (link) return link as string;
  const key = invData?.data?.[0]?.key || invitation?.key;
  if (key) return `${host.replace(/\/$/, "")}/client/quote/${key}`;
  throw new Error("No quote invitation link available");
}
