# Axis by Flat 18 — Terms of Service
**Date:** 27 August 2025

## 1. Introduction
Axis by Flat 18 (“Axis”, “we”, “our”) is a sub‑brand of Flat 18, providing affordable, fixed‑scope, rapid development sprints for MVPs, web applications, and dashboards. By engaging Axis, you (“Client”, “you”) agree to these Terms of Service. These Terms align with, and do not override, Flat 18’s overarching terms.

## 2. Scope of Work
- Axis delivers fixed‑scope projects (“Sprints”) based on published tiers (Start, Build, Pro).
- Deliverables are defined clearly at the start of each Sprint.
- Work is time‑boxed. Requests outside the agreed scope will be deferred to a future Sprint or may require a separate engagement with Flat 18.

## 3. Fees and Payment
- Sprint fees are published on the Axis website.
- Payment schedule: 50% upfront (non‑refundable), 50% before delivery.
- Work will not be launched or handed over until the final payment is received.

## 4. Intellectual Property
- Upon full payment, you own the specific deliverables created for you.
- Axis retains rights to generic tools, code accelerators, and design assets developed independently of your project.
- Axis may showcase completed work in portfolios and marketing materials unless you instruct otherwise in writing prior to launch.

## 5. Liability
- Axis provides services “as is” without warranties beyond the scope of work.
- Our total liability is limited to the fees paid by you for the relevant Sprint.
- Axis cannot guarantee investment, funding, traction, or any particular commercial outcome.

## 6. Termination
- Either party may terminate a Sprint prior to delivery. Fees already paid are non‑refundable.
- Incomplete work remains the property of Axis unless otherwise agreed in writing.

## 7. Escalation Path
- Projects requiring larger scale, complex integrations, or enterprise‑grade standards may be referred to Flat 18 under separate agreements and rates.

## 8. Governing Law
- These Terms are governed by and construed in accordance with the laws of England and Wales.