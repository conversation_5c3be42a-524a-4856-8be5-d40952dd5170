# Axis Website Redesign Directive (LottieFiles‑inspired)

**Version:** 1.0  
**Prepared for:** Axis by Flat 18 (axis-web.dev)  
**Purpose:** Translate proven layout and motion patterns from LottieFiles into a pragmatic, conversion‑centred redesign for Axis while preserving Axis’s fixed‑scope, fixed‑price MVP proposition.

---

## 1) Strategic Objectives

- **Clarity and trust at a glance.** Adopt a clean grid, generous spacing, and crisp type scale so the value proposition and pricing are immediately legible.
- **Conversion first.** Prioritise “Start a project” and “Book a call” with persistent CTAs, section‑level CTA belts, and a sticky header.
- **Proof through structure.** Present “library‑like” showcases of work and a disciplined process path (demo cards, feature rows, testimonials, deep footer) that feel familiar to users of modern product sites.
- **Motion for guidance, not gimmicks.** Use small, purposeful animations to signal affordances and progress.
- **Performance and accessibility.** Light, fast, WCAG‑compliant pages with graceful fallbacks.

---

## 2) Information Architecture

### Global navigation
- **Left:** Axis logo → Home
- **Centre (desktop):** Services, Process, Work, Pricing, Learn
- **Right:** _Start a project_ (primary), _Book a call_ (secondary), **⌘K Quick Find**
- **Behaviour:** Sticky on scroll; backdrop‑blur and subtle elevation after 8 px scroll.

### Mobile
- Hamburger → full‑screen drawer with same sections and a persistent bottom CTA pill: **Start**.
- Optional quick links row: Pricing, Process, Work.

### Sitemap
- **Home**
- **Services** (Start, Build, Pro, Scale Support)
- **Process** (four‑step method; artefacts; SLA and handover)
- **Work** (filterable grid of demos/case studies)
- **Pricing** (tiers; inclusions; guarantees; FAQs)
- **Learn** (FAQ, playbooks, stack explainer, handover checklist)
- **Start a Project** (form + chat entry)
- **Legal** (Privacy, Terms, Data Processing, Cookies)

---

## 3) Page Templates & Section Rhythm

### 3.1 Home
1. **Hero**
   - H1: _Launch an investor‑ready MVP in weeks._
   - Subline: Fixed scope, fixed price, premium polish, delivered by Flat 18.
   - CTAs: **Start a project** (primary), **Book a 15‑minute call** (secondary).
   - Right rail: animated demo tile cycling 2–3 UI screenshots with a soft slide/fade.
2. **Trust strip**
   - Monochrome logo row; gentle auto‑scroll on desktop; swipe on mobile.
3. **Axis in 60 seconds**
   - Three feature chips with micro‑animations: **Fixed scope**, **Premium polish**, **Production‑ready**.
4. **How we work (four‑step path)**
   - Horizontal stepper (Kickoff → Design → Build → Launch). On mobile, accordion.
5. **Work that shows how we build**
   - Demos/case studies grid. Card hover reveals “What we built,” “Highlights,” and “Stack” tags.
6. **Packages**
   - Three cards (Start, Build, Pro) + “Compare packages” modal.
7. **After launch**
   - Axis Scale Support summary; link to detailed page.
8. **CTA belt**
   - Full‑width stripe reinforcing the primary action.
9. **Deep footer**
   - Four columns: Services, Resources, Company, Legal, plus small badges (frameworks/tools) if desired.

### 3.2 Services (Start, Build, Pro, Scale)
- Hero with single‑line value prop and key outcomes.
- **What’s included** table, **Deliverables**, **Timeline**, **Who it’s for**, **Constraints**.
- Inline FAQs per tier.
- Sticky right‑rail card with price, timeline, and CTAs.

### 3.3 Process
- Stepper with artefacts (wireframe capture, snippet of the task board, launch checklist excerpt).
- “What we commit to” callouts per step and an SLA summary.

### 3.4 Work
- Filterable grid (by **Use case**, **Stack**, **Timeline**). Case study template: Problem, Constraints, Approach, Outcome, Handover.

### 3.5 Pricing
- Three cards + comparison modal; FAQs; assurances (fixed scope, fixed timeline, predictable cost).

### 3.6 Learn
- FAQ, playbooks, stack explainer, handover checklist. Surface via ⌘K results as well.

### 3.7 Start a Project
- Short form (name, email, use case, timeline, budget band); privacy note; live chat entry; link to book a call.

---

## 4) Visual Language

### 4.1 Typography
- **Headings:** Inter, 700/800.
- **Body/UI:** Inter, 400/500.
- **Scale:** Base 16 px; H1 48–56, H2 36–40, H3 24–28, body 16–18, captions 14.
- **Line‑height:** 1.25 headings, 1.55 body.

### 4.2 Grid & Spacing
- **Container:** 1200 px max width; 24 px gutters.
- **Grid:** 12‑column desktop; 6‑column tablet; single column mobile.
- **Spacing scale:** 4, 8, 12, 16, 24, 32, 48, 64.

### 4.3 Iconography & Illustration
- Lucide‑style stroked icons (1.75 px stroke).
- Subtle noise gradients and soft‑glass highlights on key tiles.
- Micro animation loops via dotLottie for affordances and feedback. Keep each ≤ 30 kB, looped, and theme‑aware.

---

## 5) Colour System (Light & Dark)

Axis retains a confident cobalt identity, complemented by a modern mint/teal interaction accent and a violet accent for emphasis. All body text and essential UI must meet WCAG AA.

### 5.1 Palette (Hex)
- **Primary (Cobalt):** `#2F81F7`
- **Secondary (Mint/Teal):** `#00E0B8`
- **Accent (Violet):** `#9D4BFF`
- **Success:** `#16A34A`
- **Warning:** `#EAB308`
- **Danger:** `#EF4444`
- **Neutral 0–900:** `#0B1220` (900), `#111827` (800), `#1F2937` (700), `#374151` (600), `#4B5563` (500), `#6B7280` (400), `#9CA3AF` (300), `#D1D5DB` (200), `#E5E7EB` (100), `#F8FAFC` (50), `#FFFFFF` (0)

### 5.2 Roles

**Light mode**
- `--bg: #FFFFFF`
- `--panel: #F8FAFC`
- `--text: #0B1220`
- `--muted: #4B5563`
- `--primary: #2F81F7`
- `--link: #2F81F7`
- `--cta: #00E0B8`
- Gradients: `linear-gradient(135deg, #2F81F7 0%, #00E0B8 100%)`

**Dark mode**
- `--bg: #0B1220`
- `--panel: #111827`
- `--text: #E5E7EB`
- `--muted: #9CA3AF`
- `--primary: #5EA3FF`
- `--link: #8AB9FF`
- `--cta: #35E7CC`
- Gradients: `linear-gradient(135deg, #1E3A8A 0%, #35E7CC 100%)`

**Focus Ring**
- `--focus: #FFB020` (visible on both themes).

---

## 6) Motion & Interactivity

- **Use cases:** Nav transitions, card hover lifts, CTA micro‑ripples, stepper progress, success/failure states.
- **Tone:** Fast and subtle (120–160 ms ease‑out). Spring micro‑interactions with low bounce.
- **Reduced motion:** Respect `prefers-reduced-motion`; freeze Lottie to first frame and remove parallax.
- **Lottie usage:** Prefer `.lottie` bundles; themeable, colour‑token‑aware; lazy‑load below the fold.

---

## 7) Component Specifications

### 7.1 Header
- Sticky; translucent backdrop‑blur and elevation on scroll.
- Primary CTA (filled gradient), secondary CTA (outline). ⌘K button opens command palette.

### 7.2 Hero
- Left: headline, subline, CTAs; Right: animated demo tile.
- Optional small “Created by the team at Flat 18” note.

### 7.3 Trust Strip
- Monochrome logos; auto‑scroll desktop; swipe mobile.

### 7.4 Feature Rows
- Alternating image/text. Each row includes an outcome statement and 2–3 bullet “Proof points.”

### 7.5 Work Grid
- Consistent card heights; cover image; tags for Use case, Stack, Timeline.
- Hover reveals short highlight list and a “View case” button.

### 7.6 Pricing Cards
- Outcome‑led copy. Compact list of inclusions. “Compare” modal to expand details without page jump.

### 7.7 FAQ
- Accordion, deep‑linkable items. Queryable via ⌘K.

### 7.8 Footer
- Four columns: Services, Resources, Company, Legal. Repeat primary CTA at the end.

---

## 8) Content & Voice

- **Direct and proof‑oriented.** Lead with risk reduction, handover quality, and speed with structure.
- Avoid marketing fluff. One idea per sentence, active voice.
- Every section ends with the most relevant CTA.

---

## 9) Implementation Notes

### 9.1 Stack
- **Next.js (App Router)**, **TypeScript**, **Tailwind CSS**, **shadcn/ui**, **Framer Motion**, **Radix**.

### 9.2 Theming tokens
```css
:root {
  --bg:#FFFFFF; --panel:#F8FAFC; --text:#0B1220; --muted:#4B5563;
  --primary:#2F81F7; --link:#2F81F7; --cta:#00E0B8; --focus:#FFB020;
}
:root[data-theme="dark"] {
  --bg:#0B1220; --panel:#111827; --text:#E5E7EB; --muted:#9CA3AF;
  --primary:#5EA3FF; --link:#8AB9FF; --cta:#35E7CC; --focus:#FFC155;
}
```

### 9.3 Tailwind config (excerpt)
```ts
// tailwind.config.ts
export default {
  theme: {
    extend: {
      colors: {
        bg: "var(--bg)",
        panel: "var(--panel)",
        text: "var(--text)",
        muted: "var(--muted)",
        primary: "var(--primary)",
        link: "var(--link)",
        cta: "var(--cta)",
        focus: "var(--focus)",
      },
      boxShadow: {
        sm: "0 1px 2px rgba(0,0,0,0.06)",
        md: "0 8px 24px rgba(23,34,58,0.12)",
      },
      borderRadius: { xl: "1rem", "2xl": "1.25rem" },
    }
  }
}
```

### 9.4 Components to build
- `Header`, `Footer`, `Hero`, `TrustStrip`, `FeatureRow`, `CaseCard`, `TierCard`, `StepCard`, `FAQ`, `CTABelt`, `CommandPalette`.

### 9.5 Performance
- `next/image` everywhere; only hero images get `priority`.
- Preload heading font; `display=swap`.
- Lazy‑load Lottie below the fold; compress to ≤ 30 kB per loop; static fallback frames.

### 9.6 Accessibility
- Visible skip link; keyboard‑navigable menus; labelled CTAs; focus ring token.
- Colour contrast: 4.5:1 body, 3:1 large/semibold text. Test with Axe and manual checks.

### 9.7 Analytics & SEO
- H1 per page; descriptive titles; FAQPage Schema; Service Schema for tiers.
- Track CTA clicks, form starts/completions, and scrolled‑to‑section events.

---

## 10) Deliverables Checklist

- [ ] Redlined wireframes: Home, Services ×4, Process, Work, Pricing, Learn, Start, 404  
- [ ] Component library in Storybook (or shadcn preview): cards, stepper, accordions, CTA belts  
- [ ] Theming tokens + Tailwind config  
- [ ] Motion spec per component + reduced‑motion plan  
- [ ] Lottie asset list with sizes and theme tokens  
- [ ] Copy deck aligned to voice rules  
- [ ] Accessibility and performance budgets  
- [ ] Launch checklist and post‑launch monitoring hooks

---

## 11) Migration Notes

- Preserve current content model (tiers, process, pricing) and URLs where possible.
- Re‑shoot demo screenshots with consistent frames, shadows, and lighting.
- Phase rollout: ship new shell + hero + pricing first, then Work and Learn.

---

## 12) Acceptance Criteria

- Lighthouse Performance ≥ 90 on Home and Pricing in both themes.
- All core actions reachable within 1 click on desktop, 2 taps on mobile.
- ⌘K palette returns matches for pages, FAQs, and case studies.
- All text passes WCAG AA in both themes.
- Lottie disabled or reduced under `prefers-reduced-motion` without UX loss.

---

## References

- Axis by Flat 18 (current relaunch): https://axis-web.dev/  
- LottieFiles (layout and motion cues): https://lottiefiles.com/

