CLAUDE DIRECTIVE — BUILD AXIS WEBSITE (Next.js 14)

Project root will contain the following source files. READ THEM FIRST:
- Axis_by_Flat18_Business_Plan.md
- Axis_by_Flat18_Business_Plan.html
- Axis_Terms_of_Service.md
- Axis_Privacy_Policy.md
- Axis_Cookie_Policy.md
- Axis_Accessibility_Statement.md
- Axis_Acceptable_Use_Policy.md

Goal: Generate a strikingly simple, clean, high‑converting, and high‑performing website for **Axis by Flat 18** using Next.js 14 + TypeScript, TailwindCSS, shadcn‑ui, Framer Motion, Umami, and Supabase/Formspree for lead capture.

Brand constants (from the plan):
- Name: Axis by Flat 18
- Tagline: "The centre point for rapid MVPs and apps."
- Palette: base #0D1117; text #E6EDF3; accent1 #2F81F7; accent2 #9D4BFF; accent3 #00E0B8; support #FF6B6B
- Type: Display = Space Grotesk (or Inter Tight bold); Body = Inter
- Voice: crisp, confident, geometric/architectural metaphors
- Pricing (GBP): Start 1495; Build 2995; Pro 5495; Add‑on Scale Support 750/mo

Project requirements:
1) Scaffold Next.js 14 (App Router) + TypeScript.
2) Add TailwindCSS, shadcn‑ui (buttons, cards, dialog), Framer Motion.
3) Add Umami analytics and a feature‑flag to disable in dev.
4) Implement routes:
   - "/" (Home): Hero, Tiers, Why Axis, Process Timeline, Audience Tiles, Featured Work (placeholders), Pricing Cards (+ add‑on), About/Trust, Final CTA.
   - "/work": three placeholder case studies with angled mockup images.
   - "/process": concise methodology and quality guardrails.
   - "/start": lead form (React Hook Form + Zod). Fields: name, email, company, idea (short), tier (select), timeline, budget. POST "/api/lead" to Supabase (env‑gated) or fallback to console/log.
   - "/legal": render full texts by importing the following markdown files from project root:
       * Axis_Terms_of_Service.md
       * Axis_Privacy_Policy.md
       * Axis_Cookie_Policy.md
       * Axis_Accessibility_Statement.md
       * Axis_Acceptable_Use_Policy.md
     Provide a tabbed interface or anchor nav for easy scanning.
5) Components: <Hero />, <TierCards />, <WhyAxis />, <ProcessTimeline />, <AudienceTiles />, <FeaturedWork />, <PricingCards />, <FinalCTA />, <LeadForm />.
6) Accessibility: WCAG AA contrast, visible focus ring, semantic HTML, prefers‑reduced‑motion respected. Keyboard‑navigable interactive controls.
7) Performance: target 95+ Lighthouse; use next/image, code‑splitting, prefetch CTAs. Avoid blocking JS. Ship minimal deps only.
8) SEO: set metadata in app/ layout and per route; generate sitemap.xml and robots.txt. Add Organization + Product JSON‑LD (three plans). OG image = hero still frame.
9) Assets (placeholders located in /public/placeholders):
   - video/hero-loop.mp4
   - img/tier-start.svg, img/tier-build.svg, img/tier-pro.svg
   - img/mockup-1.png, img/mockup-2.png, img/mockup-3.png
   Include alt text placeholders.
10) Pricing cards must reflect Axis Start (£1,495), Axis Build (£2,995, marked “Most Popular”), Axis Pro (£5,495), and the optional add‑on Axis Scale Support (£750/mo). Buttons link to "/start" with a preselected tier query param.
11) Header: sticky with primary CTA → "/start". Footer: brand, contact link, and /legal links.
12) Content tone: British English. Keep copy concise and premium; avoid Americanisms.

Legal pages:
- Use the full texts from the markdown files in the project root (see list above). Render them with clear headings, a right‑side anchor navigation on desktop, and simple stacked layout on mobile. Ensure these do not contradict Flat 18’s policies; they are lighter versions aligned to a sprint model.

Deliverables:
- Complete Next.js repo with all routes/components/styles wired.
- README.md describing setup, env vars (UMAMI, SUPABASE), and build/deploy steps (Vercel).

Quality gates:
- Run type‑check, build, and a Lighthouse pass locally; resolve issues.
- Validate a11y with axe (basic pass).