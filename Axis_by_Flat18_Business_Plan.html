import React, { useEffect } from "react";

declare global {
  interface Window {
    chatwoot?: { toggle: () => void };
    $chatwoot?: {
      toggle?: () => void;
      setTheme?: (theme: "dark" | "light") => void;
      setColorTheme?: (theme: "dark" | "light") => void;
      setDarkMode?: (enabled: boolean) => void;
    };
  }
}

function ensureChatwootDark() {
  try {
    // Try every known API surface; all are safe no-ops if missing.
    // @ts-ignore
    window.$chatwoot?.setTheme?.("dark");
    // @ts-ignore
    window.$chatwoot?.setColorTheme?.("dark");
    // @ts-ignore
    window.$chatwoot?.setDarkMode?.(true);
  } catch {}
  try {
    // Persisted hints used by some builds
    localStorage.setItem("cw:theme", "dark");
    localStorage.setItem("chatwoot:theme", "dark");
    localStorage.setItem("theme", "dark");
  } catch {}
}

export function ChatwootWidget() {
  useEffect(() => {
    if (typeof window === "undefined") return;

    // Provide multiple hints for dark theme across different SDK versions
    // These keys are harmless if unrecognised
    // @ts-ignore
    (window as any).chatwootSettings = {
      // commonly observed keys across releases
      darkMode: "dark",
      dark_mode: "dark",
      theme: "dark",
      colorScheme: "dark",
    };

    const s = document.createElement("script");
    s.src = "https://app.chatwoot.com/packs/js/sdk.js";
    s.async = true;
    s.defer = true;
    document.body.appendChild(s);

    // After SDK is ready, attempt to force dark theme consistently
    ensureChatwootDark();

    // Retry a bit longer to catch re-renders/late mounts
    let attempts = 0;
    const id = window.setInterval(() => {
      attempts += 1;
      ensureChatwootDark();
      if (attempts > 20) window.clearInterval(id);
    }, 400);

    // A couple of delayed passes for good measure
    window.setTimeout(ensureChatwootDark, 3000);
    window.setTimeout(ensureChatwootDark, 8000);

    return () => {
      document.body.removeChild(s);
    };
  }, []);

  return null;
}

export function openChat() {
  if (typeof window !== "undefined") {
    // Pre-emptively enforce dark, then toggle, then re-enforce after mount
    ensureChatwootDark();
    window.$chatwoot?.toggle?.();
    window.chatwoot?.toggle?.();
    window.setTimeout(ensureChatwootDark, 50);
    window.setTimeout(ensureChatwootDark, 500);
  }
}
