# Aftercare & Ongoing Support

Axis doesn’t vanish after launch. We ship, we stabilise, and we stay available so your product keeps running smoothly while you gather real‑world feedback.

---

## What’s included after handover

Every project includes a short **post‑launch stabilisation window** intended for:
- Fixing launch‑related issues that surface in production
- Minor polish and copy tweaks
- Monitoring the deployment and ensuring the basics (auth, data flows, emails, payments) behave as expected
- Handing over repos, environment variables, and credentials

**Ownership:** You own the code and infrastructure. We deliver documentation, run‑books, and a clear change log so another team could pick up the work if needed.

---

## Optional monthly support with Axis

When you need more than the stabilisation window, we offer a simple ongoing support option focused on reliability and small‑to‑medium enhancements.

**Typical inclusions**
- Bug fixes and small improvements
- Dependency updates and security patches
- Uptime and error monitoring with periodic reports
- Performance tuning for slow endpoints or pages
- Light feature work (eg. extra form fields, small screens, minor flows)
- Content updates and housekeeping (meta, OG images, redirects)

**How it works**
- You submit tasks via our support inbox or portal
- We triage, estimate, and schedule
- You receive short updates as items move from “queued” → “in progress” → “done”

We aim to keep support lightweight, predictable, and easy to pause when you don’t need it.

---

## When Flat 18 steps in

Some products outgrow “support” and become an **ongoing programme** with evolving scope, long timelines, and larger feature sets. In those cases, the work moves under **Flat 18** for structured product development:

- Road‑mapping and discovery workshops
- UX research, design systems, and complex UI
- Multi‑integration backends and data pipelines
- Compliance, security reviews, and performance work at scale
- Dedicated sprints and stakeholder management

**In short:** if your needs are continuous and expanding, Flat 18 provides the long‑term support and maintenance path.

---

## Communication & status

- **Channels:** email/ticketing by default; shared Slack channel available on request
- **Updates:** concise notes on progress and outcomes, not weekly novels
- **Incidents:** we prioritise production issues and restore service before we optimise

---

## Handover quality (what you can expect)

- Clean repositories with README, environment examples, and scripts
- Infrastructure notes for hosting and CI/CD
- Credentials transferred securely
- A simple operator checklist for routine tasks (backups, plan limits, log locations)

---

## Security & privacy

- Sensible defaults: least‑privilege access, rotated tokens, and encrypted secrets
- Regular dependency updates in support plans
- GDPR‑aware data handling; we’ll flag areas that need your policy decisions

---

## Change requests & enhancements

1. You describe the change (what problem, who it’s for, what “good” looks like).  
2. We confirm scope and impact (and advise if it’s better suited to a Flat 18 engagement).  
3. You approve the estimate; we schedule.  
4. We deliver and document the change.

---

## Billing options

- **Support retainer:** a predictable monthly plan for fixes and small enhancements  
- **Ad‑hoc:** time‑boxed support for one‑off needs  
- **Flat 18 retainer or project:** for ongoing, evolving scope

No bundles, no expiring “credits,” no surprise invoices.

---

## How to get support

- Create a request in the support portal or email us with context and screenshots
- For incidents, include the URL, timestamp, and any user error messages
- We’ll acknowledge, triage, and keep you informed until resolved

---

## FAQs

**Do you guarantee response times?**  
We prioritise production issues first and communicate clearly. If you need strict SLAs, we’ll scope that under Flat 18.

**Can you support an app not built by Axis?**  
Possibly. We’ll assess the codebase, documentation, and hosting before we commit.

**Can we bring our own monitoring or ticketing tools?**  
Yes. We’re happy to integrate with your preferred stack if it’s sensible.

**What if we want to pause support?**  
You can pause at the end of a billing period and return later. We’ll keep your documentation current.

**How do we move from Axis support to Flat 18 delivery?**  
We’ll propose a transition plan and timeline, then run a structured kick‑off under Flat 18.

---

**Summary:** Axis stays with you after launch for stability and steady improvements. When your product becomes a long‑running, evolving programme, Flat 18 provides the ongoing support and maintenance to match.
