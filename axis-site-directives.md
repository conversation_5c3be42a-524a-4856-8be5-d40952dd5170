# Axis Site Refresh: Honest, Conversion‑First Redesign
Version: 2025‑09‑17

This document is a **precise directive for Codex** to implement. It contains: non‑negotiable rules, component‑level tasks with file paths, verbatim copy blocks, design tokens, and measurement requirements. The aim is to **improve conversions** while being **transparent** about Axis being new, without claiming results we cannot verify.

---

## 0) Non‑Negotiables

1. **Transparency:** Axis is new. Do not state or imply past results, client counts, revenue raised, or success rates unless they are verifiable and approved.
2. **No vanity metrics:** Remove/avoid “50+ MVPs,” “£2M+ raised,” or similar. If any number is used, it must be a hard fact with a source and sign‑off.
3. **Tight colour discipline:** Primary accent = `--axis-cobalt`. Secondary accent = `--axis-teal`. Purple is **secondary‑subtle only**; gradients use at most two accents.
4. **Single dominant CTA:** Primary CTA text = “Start a project”. Secondary CTA text = “Book a 15‑minute call”.
5. **Mobile‑first clarity:** All sections must pass a 5‑second skim test on a 375px wide viewport.
6. **Accessibility:** Colours must pass WCAG AA for text. All interactive elements have focus states and sensible hit‑areas.
7. **No duplicate sections:** Each section must add new information. Remove repetition between Why/Process/Features.
8. **British English only.**

---

## 1) Design Tokens (Tailwind CSS variables)

Implement in `src/styles/globals.css` (or theme provider) and map to Tailwind via CSS variables.

```css
:root {
  /* Neutrals (dark theme) */
  --bg-950: #0B0F14;
  --bg-900: #0F1520;
  --bg-800: #131B26;
  --surface-700: #162131;
  --border-600: #1E2A3A;

  --text-100: #EAF2FF;
  --text-300: #C0CEE6;
  --text-500: #8FA3C2;

  /* Accents */
  --axis-cobalt: #2F81F7;
  --axis-teal:   #00E0B8;
  --axis-purple: #9D4BFF; /* use sparingly, badges only */

  /* Feedback */
  --success: #21C36F;
  --warning: #FFB020;
  --danger:  #FF4D4D;

  /* Radii & shadows */
  --radius-lg: 14px;
  --radius-md: 10px;
  --elev-1: 0 6px 24px rgba(0,0,0,0.18);
  --elev-2: 0 12px 36px rgba(0,0,0,0.24);
}
```

Tailwind utility examples:
- Container max‑widths: `max-w-[1200px]` desktop, `max-w-[680px]` text blocks.
- Headings use `font-semibold` with tight tracking. Body uses `leading-7`.
- **CTA buttons** use cobalt background, teal hover ring. Secondary buttons = outline cobalt.

---

## 2) Typography & Iconography

- Font: **Inter** (system fallbacks). Headings: `Inter 800/700`. Body: `Inter 400/500`.
- Letter‑spacing: headings `tracking-tight`, overlines `tracking-widest text-xs`.
- Icons: **Bootstrap Icons** only. Size 18–24px. Colour follows text colour.

---

## 3) Information Architecture

**Order:** Hero → Why (Problem/Solution) → Process → Work Samples → Pricing → Support → FAQ → Final CTA → Footer.

- Only one “Start a project” button per section (top‑right in nav, then section CTAs). Secondary “Book a 15‑minute call” appears in Hero, Pricing, Final CTA.
- Remove any duplicate pricing/process blocks.

---

## 4) Components: File‑level Tasks & Acceptance Criteria

### 4.1 Hero
**Files:** `src/components/hero/hero.tsx`, `src/app/page.tsx`  
**Objective:** Immediate clarity and trust, without unverified claims.

**Implementation:**
- Background: subtle dark glass with a **single** accent glow (cobalt). No rainbow streaks.
- Headline (H1) and subcopy from Verbose Copy section below.
- Primary CTA (solid cobalt): “Start a project” → `/start`.
- Secondary CTA (outline): “Book a 15‑minute call” → `/call`.
- Trust line below CTAs: “Created by the team at Flat 18.” Link to flat18.co.uk.

**Acceptance Criteria:**
- Above‑the‑fold lighthouse contrast ≥ AA.
- No metrics or numbers shown.
- Mobile: all text fits without truncation.

### 4.2 Why Axis (Problem/Solution)
**File:** `src/components/why-axis.tsx`  
**Objective:** Show we understand the founder problem; state how we work.

**Implementation:**
- Two‑column at ≥1024px; stacked on mobile.
- Left: 3 bullet pains. Right: card with our approach.
- Copy must be exactly the verbatim block provided below.

**Acceptance Criteria:**
- No repeated bullets elsewhere.
- Card uses `--surface-700` with `--elev-1` shadow.

### 4.3 Process Timeline
**File:** `src/components/process/process-timeline.tsx`  
**Objective:** Reduce perceived risk with a 4‑step plan and concrete behaviours we commit to.

**Implementation:**
- Four steps: Kickoff → Design → Build → Launch.
- Each step: title, one‑line outcome, one commitment.
- Include weekly demo note.

**Acceptance Criteria:**
- Icons use Bootstrap Icons only.
- No durations promised beyond “weeks.”

### 4.4 Work Samples
**File:** `src/components/work/featured-work.tsx`  
**Objective:** Show taste and capability without implying client outcomes we can’t verify.

**Implementation:**
- Three cards:
  1) **Axis Demo: SaaS Billing UI** (clearly labelled “Demo”).  
  2) **Axis Demo: Investor Dashboard** (“Demo”).  
  3) **Selected Work by Flat 18** (1 real project, no claims; link to full case on flat18.co.uk).
- Each card: 1 image, “What we built,” “Highlights,” “Stack.”

**Acceptance Criteria:**
- “Demo” badge visible on demo items.
- No figures (users, revenue, funding) appear.

### 4.5 Pricing
**File:** `src/components/pricing/pricing-cards.tsx`  
**Objective:** Fixed, predictable packages with scope clarity and zero hype.

**Implementation:**
- Three tiers: **Start**, **Build** (emphasised), **Pro**.
- Keep price points if they match internal policy; if not, use “from £X” until confirmed.
- Features are capabilities and deliverables, not outcomes.
- Below cards: muted disclaimer.

**Acceptance Criteria:**
- Only one tier highlighted.
- Each card max 6 bullets.
- Payment terms appear in muted text.

### 4.6 Support / Aftercare
**File:** `src/components/support-aftercare.tsx`  
**Objective:** Communicate continuity without upselling pressure.

**Implementation:**
- Two columns: “What’s included post‑launch” and “Optional ongoing support.”
- Link to `/support`.

**Acceptance Criteria:**
- No bundles, no “limited time” language.

### 4.7 FAQ
**File:** `src/components/faq.tsx`  
**Objective:** Remove objections with plain answers.

**Implementation:**
- 5–6 concise Q&As from verbatim block.
- Accordions with proper keyboard navigation.

**Acceptance Criteria:**
- Each answer ≤ 2 sentences.
- No links to external claims.

### 4.8 Final CTA
**File:** `src/components/final-cta.tsx`  
**Objective:** One last, focused action.

**Implementation:**
- Compact block with headline + both CTAs.
- Gradient background uses only cobalt→teal at 20% opacity over dark surface.

**Acceptance Criteria:**
- No third‑option links inside the block.

### 4.9 Footer
**File:** `src/components/footer.tsx`  
**Objective:** Credible contact and navigation.

**Implementation:**
- Columns: Product, Company, Contact.
- Include email, city (no full address), and Flat 18 attribution.
- Social icons optional.

**Acceptance Criteria:**
- Small‑print copyright present.
- No tracking pixels here.

---

## 5) Verbatim Copy Blocks (paste as‑is)

### 5.1 Hero
**H1:**  
```
Launch a polished MVP in weeks. Fixed scope, fixed price.
```

**Subcopy:**  
```
Axis is a small, senior team building investor‑ready design and production code. We’re new, operate transparently, and ship fast using proven patterns.
```

**Primary CTA:**  
```
Start a project
```

**Secondary CTA:**  
```
Book a 15‑minute call
```

**Trust line:**  
```
Created by the team at Flat 18.
```

---

### 5.2 Why Axis (Problem/Solution)
**Section heading:**  
```
Building your first product shouldn’t drain time or trust.
```

**Bullets (left):**  
```
• Slipping timelines and unclear scope
• Design that looks unfinished
• Code you can’t confidently ship or hand over
```

**Approach card (right):**  
```
How we work
— Fixed scope and transparent pricing
— Wireframes in days, weekly demos thereafter
— Production‑grade code, clean handover, monitoring at launch
```

---

### 5.3 Process Timeline
**Section heading:**  
```
A four‑step path to live
```

**Steps:**  
```
Kickoff — Align scope and priorities within 48 hours.
Design — Clickable wireframes and key screens in days.
Build — Iterative shipping with weekly demos.
Launch — Deploy, monitor, and hand over cleanly.
```

**Note:**  
```
We demo progress every week. No surprises.
```

---

### 5.4 Work Samples
**Section heading:**  
```
Work that shows how we build
```

**Card titles and blurbs:**  
```
Axis Demo: SaaS Billing UI
A compact subscription flow with plans, invoices, and Stripe‑ready screens.

Axis Demo: Investor Dashboard
A clean dashboard for KPIs, cohorts, and updates, designed for boardroom demos.

Selected Work by Flat 18
A real project from our parent team. No claims, just the craft.
```

---

### 5.5 Pricing
**Section heading:**  
```
Fixed‑price packages that match typical first builds
```

**Tier: Start**  
```
For simple proof‑of‑concepts.
• Core user flows and 3–5 key screens
• Basic auth and one data source
• Deployed to your cloud
• Handover and launch notes
```

**Tier: Build (emphasised)**  
```
For MVPs targeting early users.
• Designed key journeys and responsive UI
• Auth, database, and 1–3 integrations
• Production build with monitoring
• Weekly demos and clean handover
```

**Tier: Pro**  
```
For richer MVPs or stakeholder demos.
• Expanded feature set and admin
• 3–6 integrations or APIs
• Hardening, QA support
• Launch support and handover
```

**Disclaimer (below cards, small):**  
```
We don’t publish vanity metrics. If you need references, ask. We’re happy to share what we can.
```

**Secondary line (payment):**  
```
Fixed scope, clear timeline, staged payments.
```

---

### 5.6 Support / Aftercare
**Section heading:**  
```
After launch, we don’t disappear
```

**Copy:**  
```
Every project includes a short post‑launch window for stability and small fixes. If you need continued iterations or feature work, we offer a simple monthly support option.
```

**CTA:**  
```
Explore support options
```

---

### 5.7 FAQ
```
Do you build from templates?
No. We design the UI for your use‑case and use proven components where it helps speed and reliability.

Who owns the code?
You do. We hand over repos and credentials at launch.

Can you work with my existing backend?
Yes, if it’s documented and stable. We’ll assess during kickoff.

How long does a typical project take?
Weeks, not months. We scope with you and keep to an agreed plan.

What tech stack do you use?
Modern, widely‑supported tools (e.g. React/Next.js, TypeScript, Postgres). We select pragmatically.

Can you show references?
Yes. We’re new as Axis, so we share what’s appropriate from the team’s prior work on request.
```

---

### 5.8 Final CTA
**Heading:**  
```
Ready to start?
```

**Buttons:**  
```
Start a project
Book a 15‑minute call
```

---

## 6) Colour Usage Rules

- Backgrounds: `--bg-900` and `--bg-800`. Cards use `--surface-700` only.
- Primary CTAs: solid `--axis-cobalt` with teal focus ring.
- Secondary CTAs: outline cobalt; hover bg uses 8% cobalt.
- Gradients: only cobalt→teal, 12–20% opacity overlays. **No purple gradients**.
- Badges/labels: purple allowed, small areas only.

---

## 7) Imagery

- Replace rainbow hero with one abstract **cobalt‑teal** streak or a subtle depth field.
- Demo screenshots: export at 1600px width, 2× DPR, rounded corners `--radius-lg`, shadow `--elev-1`.
- Alt text required for every image. Keep descriptive, not salesy.

---

## 8) Navigation & Routes

- `/start` → short intake form (name, email, 3 questions, file upload).  
- `/call` → booking link (TidyCal/Cal.com).  
- `/support` → support options page.  
- Header: Logo (left), simple nav (Process, Work, Pricing, FAQ), CTA (right). Sticky on scroll.

---

## 9) Analytics, Events, and Experiments (Umami)

Implement Umami events:
- `cta_start_project_clicked`
- `cta_book_call_clicked`
- `pricing_build_selected`
- `intake_form_submitted`

Add a measurement note:
- Baseline over 2 weeks: visits, CTA CTRs, intake completion. No A/B until baseline is captured.

---

## 10) Content Governance

- Before deploy, **grep** for banned patterns:
  - `50\\+`, `£\\d+M`, `raised`, `investor traction`, `guarantee`, `success rate`
- Replace or remove any unverified claims.
- If in doubt, omit. The page must be truthful even if it’s simpler.

---

## 11) Performance & QA

- Target Lighthouse: Perf ≥ 90, Access ≥ 95, Best Practices ≥ 95.
- SVGs optimised; images `webp` where possible.
- Keyboard navigation end‑to‑end. Focus ring visible on all controls.

---

## 12) Deployment Checklist

- Purge CSS, build for production.
- Verify OG/meta: title “Axis — MVPs in weeks. Fixed scope, fixed price.”
- Social preview updated with new hero.
- 404 and 500 pages inherit theme.
- Sitemap and robots intact.

---

## 13) Future Enhancements (Post‑launch)

- Replace demo cards with real case studies as they ship.
- Add a lightweight “Playground” page showing UI kits and code snippets used in builds.
- Publish a single transparent post: “What we promise at Axis.”

---

**End of directive. Paste copy verbatim where specified. Do not add claims we cannot document.**
